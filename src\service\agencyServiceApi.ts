import type { OrderStatus, ServerType } from '@/enums'
import { http } from '@/utils/http'

// 参数接口
export interface OrderOtherInfoListParams {
  otherStatus?: OrderStatus // 状态，-1：办理失败，0：待提交资料，1：资料已提交、2：办理中、3：已完成
  serverType: ServerType
  userId: number
}

export interface OrderOtherInfoListResData {
  barCodeCardNum: string
  barCodeCardPassword: string
  companyLicenseChangeImage: string
  companyLicenseChangeUrl: string
  companyLicenseImage: string
  companyLicenseUrl: string
  contact: string
  contactPhone: string
  email: string
  isHasChange: boolean
  orderCode: string
  orderContent: string
  orderId: number
  otherStatus: OrderStatus
  otherStatusStr: string
  payCetificate: string
  payCetificateImage: string
  payDate: string
  reason: string
  registrationForm: string
  registrationFormImage: string
  legalPhone: string
}

// 响应接口
export interface OrderOtherInfoListRes {
  data: OrderOtherInfoListResData[]
  empty: boolean
  errCode: string
  errMessage: string
  notEmpty: boolean
  success: boolean
}
/**
 * 根据续展状态查询已购买的续展列表
 * @param {object} params qry
 * @param {number} params.otherStatus 状态，-1：办理失败，0：待提交资料，1：资料已提交、2：办理中、3：已完成
 * @param {number} params.serverType 服务类型：1：条码制作，2：信息上报,3:包装设计，4、标签印刷，5：店内码，6：续展；10：注册；12：变更；7：标签设计；8：打印机；9：进口商品报备；
 * @param {number} params.userId 用户id
 * @returns
 */
export function orderOtherInfoListApi(params: OrderOtherInfoListParams) {
  return http.post<OrderOtherInfoListRes>('/api/orderOtherInfoList', params)
}

// 响应接口
export interface UploadImageRes {
  data: {
    image: string
    imageName: string
    url: string
  }
  errCode: string
  errMessage: string
  success: boolean
}
// 上传资料接口
export const uploadOrderOtherImageApiPath = '/api/uploadOrderOtherImage'

// 参数接口
export interface OrderRenewalCreateParams {
  barCodeCardNum?: string
  barCodeCardPassword?: string
  companyLicenseChangeUrl?: string
  companyLicenseUrl: string
  contact: string
  contactPhone: string
  email: string
  isHasChange: boolean
  orderId: number
  payCetificate: string
}

// 响应接口
export interface OrderRenewalCreateRes {
  errCode: string
  errMessage: string
  success: boolean
}

/**
 * 新增-编辑续展资料
 * @param {object} params cmd
 * @param {string} params.barCodeCardNum 用户条码卡账号
 * @param {string} params.barCodeCardPassword 用户条码卡密码
 * @param {string} params.companyLicenseChangeUrl 营业执照变更证明（市场监管部门出具的）复印件加盖公章扫描件,包含变更则必须
 * @param {string} params.companyLicenseUrl 营业执执照复印件加盖公章后的图片
 * @param {string} params.contact 联系人
 * @param {string} params.contactPhone 联系电话
 * @param {string} params.email 邮件
 * @param {boolean} params.isHasChange 是否包含变更，true:包含，false:不包含
 * @param {number} params.orderId 订单id
 * @param {string} params.payCetificate 付款证明
 * @returns
 */
export function orderRenewalCreateApi(params: OrderRenewalCreateParams) {
  return http.post<OrderRenewalCreateRes>('/api/orderRenewalCreate', params)
}

// 参数接口
export interface OrderRegistrationCreateParams {
  companyLicenseUrl: string
  contact: string
  contactPhone: string
  email: string
  orderId: number
  registrationForm: string
  payCetificate: string
  legalPhone: string
}

// 响应接口
export interface OrderRegistrationCreateRes {
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 新增-编辑注册资料
 * @param {object} params cmd
 * @param {string} params.companyLicenseUrl 营业执执照复印件加盖公章后的图片
 * @param {string} params.contact 联系人
 * @param {string} params.contactPhone 联系电话
 * @param {string} params.email 邮件
 * @param {number} params.orderId 订单id
 * @param {string} params.registrationForm 注册登记表
 * @returns
 */
export function orderRegistrationCreateApi(params: OrderRegistrationCreateParams) {
  return http.post<OrderRegistrationCreateRes>('/api/orderRegistrationCreate', params)
}

// 参数接口
export interface OrderChangeCreateParams {
  barCodeCardNum?: string
  barCodeCardPassword?: string
  companyLicenseChangeUrl: string
  contact: string
  contactPhone: string
  email: string
  orderId: number
}

// 响应接口
export interface OrderChangeCreateRes {
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 新增-编辑变更资料
 * @param {object} params cmd
 * @param {string} params.barCodeCardNum 用户条码卡账号
 * @param {string} params.barCodeCardPassword 用户条码卡密码
 * @param {string} params.companyLicenseChangeUrl 营业执照变更证明（市场监管部门出具的）复印件加盖公章扫描件,包含变更则必须
 * @param {string} params.contact 联系人
 * @param {string} params.contactPhone 联系电话
 * @param {string} params.email 邮件
 * @param {number} params.orderId 订单id
 * @returns
 */
export function orderChangeCreateApi(params: OrderChangeCreateParams) {
  return http.post<OrderChangeCreateRes>('/api/orderChangeCreate', params)
}

// 参数接口
export interface IgnoreRenewalInformParams {
  gsDataId: number
}

// 响应接口
export interface IgnoreRenewalInformRes {
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 忽略续展通知
 * @param {object} params cmd
 * @param {number} params.gsDataId 企业厂商码记录id
 * @returns
 */
export function ignoreRenewalInformApi(params: IgnoreRenewalInformParams) {
  return http.post<IgnoreRenewalInformRes>('/api/ignoreRenewalInform', params)
}

// 上传退款图片地址
export const uploadRefundImagePath = '/api/uploadRefundImage'
