// 定义全局基础URL变量
$base-url: 'https://app.xunma.store';

:root,
page {
  // 修改按主题色
  --wot-color-theme: #ff5b00;
  --wot-color-success: #00b42a;
  --wot-color-warning: #ff7d00;
  --wot-color-danger: #dc3126;

  --o-body-bg-color: #f0f3f8;
  font-size: 16px;

  background-color: var(--o-body-bg-color);
  @extend %o-color-content;
}

.o-color-aid {
  color: #8c8c8c;
}

%o-color-content {
  color: #262626;
}

.o-color-content {
  @extend %o-color-content;
}

.o-bg-no {
  background: var(--o-body-bg-color);
}

.o-bg-white-disable {
  background: #e5e9ef;
}

.o-bg-primary-light {
  background: rgb(255, 241, 227);
}

.o-color-primary {
  color: var(--wot-color-theme);
}

.o-color-success {
  color: var(--wot-color-success);
}

.o-color-warning {
  color: var(--wot-color-warning);
}

.o-color-danger {
  color: var(--wot-color-danger);
}

.o-bg-primary {
  background: linear-gradient(91deg, #fa4c03 0%, #ff5b00 48.56%, #fd7424 100%);
}

.o-bg-primary-disable {
  color: #f0e0d1;
  background: #ffc895;
}

.o-bg-transparent {
  background: transparent !important;
}

.o-dot {
  $w: 10rpx;
  flex-shrink: 0;
  width: $w;
  height: $w;
  margin-top: 0.35rem;
  background: var(--wot-color-theme);
  border-radius: calc($w / 2);
}

.o-line {
  height: 1px;
  background: rgba(0, 0, 0, 0.15);
  transform: scaleY(0.5);
}

.o-label-line {
  position: relative;

  &::after {
    position: absolute;
    right: 0;
    bottom: 3px;
    left: 0;
    height: 1px;
    content: '';
    background: #dadada;
    transform: scaleY(0.5);
  }
}

.o-shadow-base {
  box-shadow: 0 10rpx 15rpx rgba(0, 0, 0, 0.05);
}

.o-shadow-deep {
  box-shadow: 0 10rpx 15rpx rgba(0, 0, 0, 0.2);
}

.o-btn-no-style {
  padding-right: 0;
  padding-left: 0;
  margin-right: 0;
  margin-left: 0;
  background: none;

  &::after {
    border: none;
  }
}

.o-barcode-gray-card {
  padding: 0.2rem 0.5rem;
  background-color: rgba(220, 225, 234, 0.7);
}

.o-border {
  border: 1px solid $uni-border-color;
}

.o-border-gray-tag {
  background: var(--o-body-bg-color);
  border: 1px solid var(--o-body-bg-color);
}

.o-border-blue-tag {
  color: var(--wot-color-theme);
  background: #fdf4f2;
  border: 1px solid var(--wot-color-theme);
}

.o-p {
  line-height: 1.5;
  text-indent: 2em;
}

.o-logo {
  background-image: url('#{$base-url}/images/logo_big.png');
  background-repeat: no-repeat;
  background-position: center center;
  background-size: contain;
}

.o-row-scroll {
  max-width: 100%;
  overflow-x: auto;
}

.o-cs-img {
  $w: 65rpx;
  width: $w !important;
  height: $w !important;
}

.o-cs-img-small {
  $w: 55rpx;
  width: $w;
  height: $w;
}

.o-customer-service-long {
  width: 100%;
  color: rgba(0, 0, 0, 0.85);
  background-color: #fff;
}

.o-tag {
  padding: 0 0.36rem;
  font-size: 0.7rem;
}

//-----------------------------------

.o-vf-up-radio-group {
  .u-radio__label-wrap.radio--u-radio__label-wrap {
    @apply grow-1;
  }

  .u-radio__label-wrap {
    @apply grow-1;
  }
}

/*  #ifndef MP-TOUTIAO  */
.o-vf-up-radio-group {
  .u-radio {
    @apply w-full;

    margin-top: 0 !important;
    margin-bottom: 0 !important;

    &:nth-of-type(even) {
      @apply py-1;
      background-color: var(--o-body-bg-color);
    }
  }
}
/*  #endif  */
/*  #ifdef MP-TOUTIAO  */
.o-vf-radio-group {
  & > label:nth-of-type(even) {
    background-color: var(--o-body-bg-color);
  }
}
/*  #endif  */
/*  #ifndef MP-TOUTIAO  */
.o-vf-radio-group {
  & > Label:nth-of-type(even) {
    background-color: var(--o-body-bg-color);
  }
}
/*  #endif  */
