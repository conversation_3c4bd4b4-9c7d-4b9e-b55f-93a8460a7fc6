<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "红包卡券",
    "enablePullDownRefresh": true,
    "backgroundColor": "#f0f3f8"
  }
}
</route>

<script lang="ts" setup>
import type { CouponPageRes } from '@/service/orderApi'
import { storeToRefs } from 'pinia'
import CouponCard from '@/components/Price/CouponCard.vue'
import { OrderDirection } from '@/enums/httpEnum'
import { couponPageApi } from '@/service/orderApi'
import { useCouponStore } from '@/store/couponStore'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()
const { userId } = storeToRefs(userStore)
const { bestPriceItem, isSelectedCoupon } = storeToRefs(useCouponStore)

const showEmptyIcon = ref(false)
const page = ref(1)
const toSelect = ref(false)
const listTimeOut = ref([])
const attrDetailValueId = ref(0)
const originalPrice = ref(0)

/**
 * 禁止选择优惠券，返回true代表禁选
 * 支付时筛选优惠券时，筛选厂商识别代码，不满足时灰色
 * @param data
 * @param openDisabled 是否开启可选判断
 */
function canSelect(data: CouponPageRes['data'][number], openDisabled: boolean) {
  // 是否开启禁止判断
  // if (!openDisabled) return false
  if (openDisabled) {
    // 是否过期
    if (data.isTimeOut === 1)
      return false
    // 厂商识别码是否符合，服务类型，1：条码制作，2：信息上报
    if (
      data.attrDetailValueId !== attrDetailValueId.value
    ) {
      return false
    }
    // 券最低使用价格
    if (data.minUsagePrice !== null && originalPrice.value < data.minUsagePrice)
      return false
    return true
  }
  return false
}

const {
  loading,
  error,
  data: list,
  run,
} = useRequest(() =>
  couponPageApi({
    isUsed: 0,
    isTimeOut: 0,
    groupBy: '',
    needTotalCount: true,
    orderBy: 'grantDate',
    orderDirection: OrderDirection.desc,
    pageIndex: 1,
    pageSize: 100000,
    userId: userId.value,
  }),
)

const {
  loading: loadingTimeOut,
  error: errorTimeOut,
  data: dataTimeOut,
  run: runTimeOut,
} = useRequest(() =>
  couponPageApi({
    isUsed: 0,
    isTimeOut: 1,
    groupBy: '',
    needTotalCount: true,
    orderBy: 'grantDate',
    orderDirection: OrderDirection.desc,
    pageIndex: page.value,
    pageSize: 10,
    userId: userId.value,
  }),
)

function init() {
  return new Promise((resolve, reject) => {
    listTimeOut.value = []
    runTimeOut()
      .then(() => {
        listTimeOut.value = [...dataTimeOut.value]
        resolve(true)
      })
      .catch((err) => {
        reject(err)
      })
  })
}

onPullDownRefresh(() => {
  showEmptyIcon.value = false
  page.value = 1
  init().finally(() => {
    uni.stopPullDownRefresh()
  })
})

// 滚到页面底部加载更多，只针对过期券
onReachBottom(() => {
  if (!showEmptyIcon.value) {
    page.value++
    runTimeOut().then(() => {
      listTimeOut.value = [...listTimeOut.value, ...dataTimeOut.value]
      if (dataTimeOut.value.length === 0) {
        showEmptyIcon.value = true
      }
    })
  }
})

function handleSelect(data: CouponPageRes['data'][number]) {
  if (canSelect(data, toSelect.value)) {
    // 选择优惠券
    bestPriceItem.value = data
    isSelectedCoupon.value = true
    uni.navigateBack()
  }
}

onLoad((option: any) => {
  // 开启选择模式
  toSelect.value = option?.toSelect === 'true'
  attrDetailValueId.value = Number(option?.attrDetailValueId)
  originalPrice.value = Number(option?.originalPrice)
  run()
  init()
})
</script>

<template>
  <view class="px-3 pb-10 pt-3">
    <view v-if="toSelect" class="py-3">
      请选择优惠券：
    </view>
    <view
      v-for="item in list"
      :key="item.couponId"
      :class="!toSelect || canSelect(item, toSelect) ? 'bg-white' : 'o-bg-white-disable'"
      class="relative mb-3 overflow-hidden rounded p-4"
      @click="handleSelect(item)"
    >
      <coupon-card :data="item" :disabled="toSelect && !canSelect(item, toSelect)" />
    </view>
    <up-empty
      v-if="list?.length === 0"
      icon="https://app.xunma.store/images/common/content.png"
      text="暂无优惠券"
    />
    <view v-if="listTimeOut?.length > 0">
      <view class="py-3">
        已过期优惠券：
      </view>
      <view
        v-for="item in listTimeOut"
        :key="item.couponId"
        class="relative mb-3 overflow-hidden rounded bg-white p-4"
      >
        <coupon-card :data="item" :disabled="!canSelect(item, toSelect)" />
      </view>
    </view>
    <view v-if="showEmptyIcon" class="o-color-aid mt-4 w-full text-center text-xs">
      - 已经到底了 -
    </view>
  </view>
</template>

<style lang="scss" scoped></style>
