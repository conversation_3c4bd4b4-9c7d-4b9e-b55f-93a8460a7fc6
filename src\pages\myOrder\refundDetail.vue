<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "售后详情",
    "backgroundColor": "#f0f3f8"
  }
}
</route>

<script lang="ts" setup>
import type { RefundDetailRes } from '@/service/orderApi'
import PriceBox from '@/components/Price/PriceBox.vue'
import { RefundState } from '@/enums'
import { Color } from '@/enums/colorEnum'
import { cancelRefundApi, refundDetailApi } from '@/service/orderApi'

// 基础URL
const baseUrl = import.meta.env.VITE_SERVER_BASEURL || ''

// 页面状态
const loading = ref(false)
const refundOrderId = ref<number>(0)
const refundDetail = ref<RefundDetailRes['data']>()
const imageList = ref<string[]>([])

// 获取页面参数
onLoad((options) => {
  if (options.refundOrderId) {
    refundOrderId.value = Number(options.refundOrderId)
    getRefundDetail()
  }
})

// 获取售后详情
function getRefundDetail() {
  loading.value = true
  refundDetailApi({ refundOrderId: refundOrderId.value })
    .then((res) => {
      if (res.success) {
        refundDetail.value = res.data
        // 处理图片列表
        if (res.data.images) {
          imageList.value = res.data.images.split(',').map((img) => {
            // 如果图片路径已经是完整URL，则直接使用
            if (img.startsWith('http://') || img.startsWith('https://')) {
              return img
            }
            // 否则添加基础URL前缀
            return `${baseUrl}/upload${img}`
          })
        }
      }
      else {
        uni.showToast({
          title: res.errMessage || '获取售后详情失败',
          icon: 'none',
          duration: 2000,
        })
      }
    })
    .catch((error) => {
      console.error('获取售后详情失败:', error)
      uni.showToast({
        title: '获取售后详情失败，请重试',
        icon: 'none',
        duration: 2000,
      })
    })
    .finally(() => {
      loading.value = false
    })
}

// 获取退款状态颜色
function getRefundStateColor(state: RefundState) {
  switch (state) {
    case RefundState.applying:
      return 'text-orange-500'
    case RefundState.success:
      return 'text-green-500'
    case RefundState.refuse:
      return 'text-red-500'
    case RefundState.cancel:
      return 'text-gray-500'
    default:
      return 'text-gray-600'
  }
}

// 预览图片
function previewImage(current: string) {
  uni.previewImage({
    urls: imageList.value,
    current,
  })
}

// 取消售后申请
function handleCancelRefund() {
  uni.showModal({
    title: '取消售后',
    content: '确定要取消此售后申请吗？',
    success: (res) => {
      if (res.confirm) {
        loading.value = true
        cancelRefundApi({ refundOrderId: refundOrderId.value })
          .then((res) => {
            if (res.success) {
              uni.showToast({
                title: '售后申请已取消',
                icon: 'success',
                duration: 2000,
              })
              // 刷新页面数据
              getRefundDetail()
            }
            else {
              uni.showToast({
                title: res.errMessage || '取消失败',
                icon: 'none',
                duration: 2000,
              })
            }
          })
          .catch((error) => {
            console.error('取消售后申请失败:', error)
            uni.showToast({
              title: '取消失败，请重试',
              icon: 'none',
              duration: 2000,
            })
          })
          .finally(() => {
            loading.value = false
          })
      }
    },
  })
}

// 返回上一页
function goBack() {
  uni.navigateBack()
}
</script>

<template>
  <view class="page-container">
    <view v-if="loading" class="loading-container">
      <up-loading-icon mode="circle" :color="Color.primary" size="36" />
      <view class="mt-2 text-gray-600">
        加载中...
      </view>
    </view>

    <template v-else-if="refundDetail">
      <!-- 状态卡片 -->
      <view class="mb-2 bg-white p-4">
        <view class="flex items-center justify-between">
          <view class="text-lg font-bold">
            售后状态
          </view>
          <view
            :class="getRefundStateColor(refundDetail.refundState)"
            class="text-base font-medium"
          >
            {{ refundDetail.refundStateStr }}
          </view>
        </view>

        <view v-if="refundDetail.message" class="mt-2 text-sm text-gray-600">
          <view>
            <text class="text-gray">
              商家回复：
            </text>
            {{ refundDetail.message }}
          </view>
        </view>

        <view
          v-if="refundDetail.refundDate && refundDetail.refundState === RefundState.success"
          class="mt-2 text-sm text-gray-600"
        >
          <view>售后时间：{{ refundDetail.refundDate }}</view>
        </view>
      </view>

      <!-- 订单信息 -->
      <view class="mb-2 bg-white p-4">
        <view class="mb-3 text-base font-bold">
          订单信息
        </view>

        <view class="mb-3 flex items-center">
          <image
            v-if="refundDetail.goodsImage"
            :src="refundDetail.goodsImage"
            mode="aspectFill"
            class="goods-image"
          />
          <view class="ml-3 flex-1">
            <view class="text-base font-medium">
              {{ refundDetail.goodsName }}
            </view>
            <view v-if="refundDetail.specValueNameStr" class="mt-1 text-sm text-gray-500">
              {{ refundDetail.specValueNameStr }}
            </view>
            <view class="mt-2 flex justify-between">
              <price-box :price="refundDetail.price" :size="28" />
              <view class="text-gray-500">
                x{{ refundDetail.number }}
              </view>
            </view>
          </view>
        </view>

        <view class="flex justify-between border-t border-gray-100 py-2 text-sm">
          <view class="text-gray-600">
            订单编号：
          </view>
          <view class="text-gray-800">
            {{ refundDetail.orderCode }}
          </view>
        </view>

        <view class="flex justify-between py-2 text-sm">
          <view class="text-gray-600">
            申请时间：
          </view>
          <view class="text-gray-800">
            {{ refundDetail.applyDate }}
          </view>
        </view>

        <view class="flex justify-between py-2 text-sm">
          <view class="text-gray-600">
            退款金额：
          </view>
          <price-box
            :price="refundDetail.actuallyRefundPrice || 0"
            :size="28"
            color="text-red-500"
          />
        </view>
      </view>

      <!-- 退款原因 -->
      <view class="mb-2 bg-white p-4">
        <view class="mb-2 text-base font-bold">
          售后原因及需求
        </view>
        <view class="text-sm text-gray-800 leading-relaxed">
          {{ refundDetail.refundReason }}
        </view>
      </view>

      <!-- 图片凭证 -->
      <view v-if="imageList.length > 0" class="mb-2 bg-white p-4">
        <view class="mb-3 text-base font-bold">
          图片凭证
        </view>
        <view class="image-grid">
          <view
            v-for="(image, index) in imageList"
            :key="index"
            class="image-item"
            @click="previewImage(image)"
          >
            <image :src="image" mode="aspectFill" class="evidence-image" />
          </view>
        </view>
      </view>

      <!-- 商家备注 -->
      <view v-if="refundDetail.remark" class="mb-2 bg-white p-4">
        <view class="mb-2 text-base font-bold">
          商家备注
        </view>
        <view class="text-sm text-gray-800 leading-relaxed">
          {{ refundDetail.remark }}
        </view>
      </view>

      <!-- 底部按钮 -->
      <view
        v-if="refundDetail.refundState === RefundState.applying"
        class="fixed-bottom flex justify-end px-4 pt-2 pb-safe"
      >
        <view class="btn bg-primary text-white" @click="handleCancelRefund">
          取消售后申请
        </view>
      </view>

      <view v-if="refundDetail.refundState === RefundState.applying" class="h-24" />
    </template>

    <view v-else class="empty-container">
      <up-icon name="info-circle" size="48" color="#c0c4cc" />
      <view class="mt-3 text-gray-500">
        未找到售后信息
      </view>
      <view class="btn btn-primary mt-4" @click="goBack">
        返回
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.page-container {
  min-height: 100vh;
  background-color: #f0f3f8;
  padding: 12px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
}

.goods-image {
  width: 80px;
  height: 80px;
  border-radius: 4px;
}

.image-grid {
  display: flex;
  flex-wrap: wrap;
}

.image-item {
  width: 80px;
  height: 80px;
  margin-right: 8px;
  margin-bottom: 8px;
}

.evidence-image {
  width: 100%;
  height: 100%;
  border-radius: 4px;
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  box-shadow: 0 -2px 6px rgba(0, 0, 0, 0.05);
}

.btn {
  padding: 10px 20px;
  border-radius: 4px;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
}
</style>
