import uviewPlus from 'uview-plus'
import { createSSRApp } from 'vue'
import App from './App.vue'
import { prototypeInterceptor, requestInterceptor } from './interceptors'
import store from './store'
import '@/style/index.scss'
import 'uno.css'
import '@/style/myStyle.scss'
// import { PublicImgPath } from './components/image'

export function createApp() {
  const app = createSSRApp(App)
  app.use(store)
  // app.use(routeInterceptor) // 不需要路由守卫
  app.use(requestInterceptor)
  app.use(prototypeInterceptor)
  app.use(uviewPlus)
  return {
    app,
  }
}
