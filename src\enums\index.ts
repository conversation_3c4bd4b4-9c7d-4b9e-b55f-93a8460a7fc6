// 图片上传最大大小 3M
export const UPLOAD_IMG_MAXSIZE = 1024 * 1024 * 3
export const BASE64IMG_PREFIX = 'data:image/jpeg;base64,'

export enum BarType {
  EAN13 = 'EAN13',
  ITF14 = 'ITF14',
}

/**
 * 支付状态
 * @param new 新建：-1
 * @param notPay 未支付：0
 * @param paid 已支付：1
 * @param failed 支付失败：2
 */
export enum PayState {
  new = -1,
  notPay = 0,
  paid = 1,
  failed = 2,
}

/**
 * 协议id
 * @param privacy 隐私协议
 * @param platform 平台免责声明
 */
export enum Article {
  platform = 96, // 讯码智联平台用户服务协议
}

export function getArticleName(article: Article) {
  switch (article) {
    case Article.platform:
      return '《平台用户服务协议》'
    default:
      return ''
  }
}

/**
 * 优惠券类型
 * @param no 默认值0，前端用于默认为优惠券
 * @param cash 代金券1
 * @param discount 折扣券2
 */
export enum CouponType {
  no = 0, // 前端用于默认为优惠券
  cash = 1, // 代金券
  discount = 2, // 折扣券
}
export function couponTypeStr(couponType: CouponType) {
  switch (couponType) {
    case 1:
      return '代金券'
    case 2:
      return '折扣券'
    default:
      return '优惠券'
  }
}

/**
 * 服务类型
 */
export enum ServerType {
  shopMall = 4, // 商城
}
const serverTypeMap: Map<ServerType, string> = new Map([
  [ServerType.shopMall, '商城'],
])

export function getServerTypeStr(serverType: ServerType) {
  return serverTypeMap.get(serverType) || ''
}

/**
 * 商品状态
 * @param cache 缓存商品：0
 * @param unListed 未上架：1
 * @param listed 已上架：2
 */
export enum GoodsStatus {
  cache = 0,
  unListed = 1,
  listed = 2,
}

/**
 * 单一/多规格
 * @param single 单规格：1
 * @param multi 多规格：2
 */
export enum SpecType {
  single = 1,
  multi = 2,
}

export enum GoodsIdEnum {
  printer = 1,
  labelPrint = 3,
}

/**
 * 发票状态，1：已开票，0：未申请,2：已申请,-1:废票
 */
export enum InvoiceState {
  failed = -1, // 废票
  notApply = 0, // 未申请
  done = 1, // 已开票
  applying = 2, // 已申请
}

export enum FromPlatform {
  wx = 1,
  douYin = 2,
}

/**
 * 订单状态
 * @param waitingSend 0 待发货
 * @param waitingReceive 1 待收货
 * @param waitingEvaluate 2 已收货，待评价
 * @param done 3 已完成
 * @param afterSale 4 售后申请中
 * @param afterSaleEnd 5 售后结束
 */
export enum OrderStatus {
  waitingSend = 0, // 待发货
  waitingReceive = 1, // 待收货
  waitingEvaluate = 2, // 已收货，待评价
  done = 3, // 已完成
  afterSale = 4, // 售后申请中
  afterSaleEnd = 5, // 售后结束
}

/**
 * 退款状态
 * @param cancel 取消售后
 * @param refuse 拒绝退款
 * @param applying 申请中
 * @param success 退款成功
 */
export enum RefundState {
  cancel = -2, // 取消售后
  refuse = -1, // 拒绝退款
  applying = 0, // 申请中
  success = 1, // 退款成功
}
