<script setup lang="ts">
import { jumpToWeChatCustomerService } from '@/hooks/useCustomerService'

withDefaults(
  defineProps<{
    content?: string
  }>(),
  {
    content: '咨询客服',
  },
)
</script>

<template>
  <button
    class="o-customer-service-long mt-3 flex items-center justify-center p-3 text-sm space-x-1"
    @click="jumpToWeChatCustomerService"
  >
    <up-icon :size="30" name="chat" />
    <text>{{ content }}</text>
  </button>
</template>

<style scoped lang="scss"></style>
