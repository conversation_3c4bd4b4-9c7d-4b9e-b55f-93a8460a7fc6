<script lang="ts" setup>
import dayjs from 'dayjs'
import { Color } from '@/enums/colorEnum'

// 定义评论数据类型
interface CommentData {
  avatarUrl: string
  commentContent: string
  commentId: number
  commentTime: string
  goodsId: number
  images: string
  nickName: string
  score: number
  userCode: string
  userId: number
}

// 定义组件props
interface Props {
  comment: CommentData
  showBorder?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showBorder: true,
})

// 定义事件
const emit = defineEmits<{
  previewImage: [images: string[], index: number]
}>()

// 处理评价图片
function getCommentImages(imagesStr: string) {
  if (!imagesStr)
    return []
  const arr = imagesStr.split(',')
  // 最后一个元素如果为空，则去掉
  if (!arr[arr.length - 1]) {
    arr.pop()
  }
  return arr
}

// 预览评价图片
function handlePreviewImage(images: string[], index: number) {
  emit('previewImage', images, index)
}
</script>

<template>
  <view class="py-4" :class="{ 'border-b border-gray-200': showBorder }">
    <view class="mb-2 flex items-center justify-between">
      <up-avatar class="shrink-0" :size="30" :src="comment.avatarUrl" />
      <view class="ml-2 flex-1 overflow-hidden">
        <view class="text-sm font-bold">
          {{ comment.nickName || '匿名用户' }}
        </view>
        <view class="flex text-xs text-gray-500">
          {{ dayjs(comment.commentTime).format('YYYY-MM-DD') }}
        </view>
      </view>
      <up-rate
        v-model="comment.score"
        class="shrink-0"
        :active-color="Color.primary"
        size="16"
        readonly
        gutter="2"
      />
    </view>

    <view class="my-2">
      {{ comment.commentContent }}
    </view>

    <view v-if="comment.images" class="mt-2 flex flex-wrap gap-2">
      <view
        v-for="(img, imgIndex) in getCommentImages(comment.images)"
        :key="imgIndex"
        class="relative overflow-hidden rounded"
        @click="handlePreviewImage(getCommentImages(comment.images), imgIndex)"
      >
        <up-image :src="img" :width="80" :height="80" mode="aspectFill" class="rounded" />
      </view>
    </view>
  </view>
</template>
