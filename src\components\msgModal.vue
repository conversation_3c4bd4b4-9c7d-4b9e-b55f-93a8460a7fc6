<script setup lang="ts">
import { Color } from '@/enums/colorEnum'
import { msgModalStore } from '@/store/msgModalStore'

const useMsgModalStore = msgModalStore()
</script>

<template>
  <up-modal
    :show="useMsgModalStore.isGlobalModalShow"
    :title="useMsgModalStore.title"
    :content="useMsgModalStore.content"
    :confirm-text="useMsgModalStore.confirmText"
    :show-cancel-button="useMsgModalStore.showCancelButton"
    :close-on-click-overlay="useMsgModalStore.closeOnClickOverlay"
    :confirm-color="Color.primary"
    @confirm="useMsgModalStore.handleConfirm"
    @cancel="useMsgModalStore.isGlobalModalShow = false"
    @close="useMsgModalStore.isGlobalModalShow = false"
  />
</template>

<style scoped lang="scss"></style>
