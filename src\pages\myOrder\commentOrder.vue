<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "商品评价"
  }
}
</route>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { UPLOAD_IMG_MAXSIZE } from '@/enums'
import { Color } from '@/enums/colorEnum'
import { mallCommentRecordCreateApi } from '@/service/shoppingMallApi'
import { commentImageUploadPath, getStsTokenApi } from '@/service/stsTokenApi'
import { useCommentStore } from '@/store/commentStore'
import { useUserStore } from '@/store/user'
import { generateOSSSignatureInfo, validateOSSConfig } from '@/utils/ossUtils'

const userStore = useUserStore()
const commentStore = useCommentStore()
const { userId } = storeToRefs(userStore)
const { commentInfo } = storeToRefs(commentStore)

// 页面状态
const loading = ref(false)
const commentImages = ref([])
const maxImageCount = 9

// OSS配置
const ossConfig = reactive({
  url: '',
  accessKeyId: '',
  accessKeySecret: '',
  securityToken: '',
  policy: '',
  signature: '',
})

// 表单数据
const commentForm = reactive({
  orderId: 0,
  commentContent: '',
  score: 5,
  images: '',
  userId: 0,
})

// 初始化数据
onLoad(() => {
  if (commentInfo.value.orderId) {
    commentForm.orderId = commentInfo.value.orderId
    commentForm.userId = userId.value
    // 获取STS临时凭证
    getOssToken()
  }
  else {
    uni.showToast({
      title: '参数错误',
      icon: 'none',
      duration: 2000,
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 2000)
  }
})

// 页面卸载时清空商品信息
onUnload(() => {
  commentStore.clearCommentInfo()
})

// 获取OSS临时访问凭证
async function getOssToken() {
  try {
    const res = await getStsTokenApi()
    console.log('获取STS凭证响应:', res)

    if (res.success) {
      ossConfig.accessKeyId = res.data.accessKeyId
      ossConfig.accessKeySecret = res.data.accessKeySecret
      ossConfig.securityToken = res.data.securityToken
      // 实际项目中，OSS的URL应该从环境变量或后端获取
      ossConfig.url = import.meta.env.VITE_OSS_UPLOAD_URL || ''
      console.log('OSS配置URL:', ossConfig.url)

      // 根据STS凭证生成policy和signature
      try {
        const signatureInfo = generateOSSSignatureInfo(res.data.accessKeySecret, {
          expiration: 3600, // 1小时过期
          maxFileSize: UPLOAD_IMG_MAXSIZE, // 使用项目中定义的最大文件大小
          keyPrefix: commentImageUploadPath, // 使用评论图片上传路径作为前缀
          successActionStatus: '200',
        })

        ossConfig.policy = signatureInfo.policy
        ossConfig.signature = signatureInfo.signature

        console.log('OSS签名生成成功:', {
          policy: `${ossConfig.policy.substring(0, 50)}...`,
          signature: `${ossConfig.signature.substring(0, 20)}...`,
        })

        // 验证OSS配置是否完整
        if (!validateOSSConfig(ossConfig)) {
          throw new Error('OSS配置不完整')
        }

        console.log('OSS配置验证通过')
      }
      catch (signError) {
        console.error('生成OSS签名失败:', signError)
        uni.showToast({
          title: '生成上传签名失败',
          icon: 'none',
          duration: 2000,
        })
        return
      }

      console.log('获取STS凭证成功，配置完成')
    }
    else {
      console.error('获取STS凭证失败:', res.errMessage)
      uni.showToast({
        title: '获取上传凭证失败',
        icon: 'none',
        duration: 2000,
      })
    }
  }
  catch (error) {
    console.error('获取STS凭证异常:', error)
    uni.showToast({
      title: '获取上传凭证失败',
      icon: 'none',
      duration: 2000,
    })
  }
}

// 生成随机字符串
function randomString(len: number): string {
  const chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'
  const maxPos = chars.length
  let str = ''
  for (let i = 0; i < len; i++) {
    str += chars.charAt(Math.floor(Math.random() * maxPos))
  }
  return str
}

// 获取文件后缀
function getSuffix(name: string): string {
  return name.substring(name.lastIndexOf('.'))
}

// OSS上传函数
function ossUpload(filePath: string, dir: string) {
  return new Promise<{
    success: boolean
    url?: string
    image?: string
    errorMessage?: string
  }>((resolve, reject) => {
    // 验证OSS配置是否完整
    if (!validateOSSConfig(ossConfig)) {
      const errorMsg = 'OSS配置不完整，请重新获取上传凭证'
      console.error(errorMsg, ossConfig)
      reject(new Error(errorMsg))
      return
    }

    const name = filePath.substring(filePath.lastIndexOf('/') + 1)
    const extension = getSuffix(name)
    // 生成包含时间戳的文件名：目录/时间戳_随机字符串.扩展名
    const timestamp = new Date().getTime()
    const key = `${dir}/${timestamp}_${randomString(6)}${extension}`

    console.log('开始OSS上传:', {
      filePath,
      key,
      url: ossConfig.url,
      hasPolicy: !!ossConfig.policy,
      hasSignature: !!ossConfig.signature,
    })

    uni.uploadFile({
      url: ossConfig.url,
      filePath,
      name: 'file',
      formData: {
        name,
        key,
        'OSSAccessKeyId': ossConfig.accessKeyId,
        'success_action_status': '200',
        'policy': ossConfig.policy,
        'signature': ossConfig.signature,
        'x-oss-security-token': ossConfig.securityToken,
      },
      success: (uploadRes) => {
        console.log('OSS上传成功响应:', uploadRes)

        // 确保URL是完整的，包含协议前缀
        let fullUrl = ossConfig.url
        if (!fullUrl.endsWith('/')) {
          fullUrl += '/'
        }
        fullUrl += key

        if (!fullUrl.startsWith('http')) {
          fullUrl = `https://${fullUrl}`
        }

        console.log('上传成功，完整URL:', fullUrl)
        resolve({
          success: true,
          url: fullUrl,
          image: key.split('/').pop(),
        })
      },
      fail: (error) => {
        console.error('OSS上传失败:', error)
        const errorMsg = error.errMsg || '上传失败'
        reject(new Error(errorMsg))
      },
    })
  })
}

// 预览图片
function previewImage(current: string) {
  // 收集所有成功上传的图片URL
  const urls = commentImages.value.filter(img => img.status === 'success').map(img => img.url)

  if (urls.length > 0) {
    uni.previewImage({
      urls,
      current,
    })
  }
}

// 选择图片
function chooseImages() {
  if (commentImages.value.length >= maxImageCount) {
    uni.showToast({
      title: `最多只能上传${maxImageCount}张图片`,
      icon: 'none',
      duration: 2000,
    })
    return
  }

  const count = maxImageCount - commentImages.value.length

  uni.chooseImage({
    count,
    sizeType: ['original', 'compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      handleCommentFileUpload(res)
    },
    fail: (err) => {
      console.error('选择图片失败:', err)
    },
  })
}

// 删除评价图片
function deleteCommentImage(index: number) {
  commentImages.value.splice(index, 1)
  updateCommentImagesString()
}

// 处理评价图片上传
async function handleCommentFileUpload(res: UniApp.ChooseImageSuccessCallbackResult) {
  const files = res.tempFilePaths as string[]
  let fileListLen = commentImages.value.length

  console.log('准备上传图片:', files.length, '张')

  // 添加上传状态
  for (let i = 0; i < files.length; i++) {
    const path = files[i]
    commentImages.value.push({
      path,
      status: 'uploading',
      message: '上传中',
      url: path,
    })
  }

  // 逐个上传并更新状态
  for (let i = 0; i < files.length; i++) {
    try {
      console.log(`开始上传第 ${i + 1}/${files.length} 张图片`)
      const item = commentImages.value[fileListLen]
      const fileUrl = files[i]

      console.log('文件URL:', fileUrl)
      const result = await uploadCommentFilePromise(fileUrl)

      console.log('上传成功，结果:', result)

      // 更新文件状态
      commentImages.value.splice(fileListLen, 1, {
        ...item,
        status: 'success',
        message: '',
        // 使用OSS返回的完整URL作为预览图片地址
        url: result.url || '',
        realUrl: result.url,
      })
      fileListLen++
    }
    catch (error) {
      console.error(`第 ${i + 1}/${files.length} 张图片上传失败:`, error)

      // 上传失败处理
      const item = commentImages.value[fileListLen]
      commentImages.value.splice(fileListLen, 1, {
        ...item,
        status: 'failed',
        message: `上传失败: ${(error as Error).message || '未知错误'}`,
      })
      fileListLen++

      uni.showToast({
        title: `图片上传失败: ${(error as Error).message || '未知错误'}`,
        icon: 'none',
        duration: 2000,
      })
    }
  }

  // 更新图片字符串
  updateCommentImagesString()
}

// 上传评价图片Promise
async function uploadCommentFilePromise(url: string) {
  try {
    if (!url) {
      throw new Error('文件路径为空')
    }

    console.log('开始上传图片路径:', url)

    // 使用ossUpload上传到OSS
    const result = await ossUpload(url, commentImageUploadPath)
    console.log('上传结果:', result)

    if (result.success) {
      return result
    }
    else {
      console.error('上传失败，错误信息:', result.errorMessage)
      uni.showToast({
        title: `图片上传失败: ${result.errorMessage || '未知错误'}`,
        icon: 'none',
        duration: 2000,
      })
      throw new Error(result.errorMessage || '上传失败')
    }
  }
  catch (error) {
    console.error('上传失败:', error)
    uni.showToast({
      title: '图片上传失败，请重试',
      icon: 'none',
      duration: 2000,
    })
    throw error
  }
}

// 更新评价图片字符串
function updateCommentImagesString() {
  const imageUrls = commentImages.value
    .filter(item => item.status === 'success' && item.realUrl)
    .map(item => item.realUrl)
  commentForm.images = imageUrls.join(',')
}

// 提交评价
function submitComment() {
  if (loading.value)
    return

  if (!commentForm.commentContent.trim()) {
    uni.showToast({
      title: '请输入评价内容',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  // 检查是否有上传失败的图片
  const hasFailedImages = commentImages.value.some(item => item.status === 'failed')
  if (hasFailedImages) {
    uni.showToast({
      title: '有图片上传失败，请重新上传或删除',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  // 检查是否有正在上传的图片
  const hasUploadingImages = commentImages.value.some(item => item.status === 'uploading')
  if (hasUploadingImages) {
    uni.showToast({
      title: '图片正在上传中，请稍候',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  loading.value = true

  mallCommentRecordCreateApi({
    orderId: commentForm.orderId,
    userId: commentForm.userId,
    commentContent: commentForm.commentContent.trim(),
    score: commentForm.score,
    images: commentForm.images,
  })
    .then((res) => {
      if (res.success) {
        uni.showToast({
          title: '评价成功',
          icon: 'success',
          duration: 2000,
        })
        setTimeout(() => {
          uni.navigateBack()
        }, 2000)
      }
      else {
        uni.showToast({
          title: res.errMessage || '评价失败',
          icon: 'none',
          duration: 2000,
        })
      }
    })
    .catch((err) => {
      uni.showToast({
        title: err.errMessage || '评价失败',
        icon: 'none',
        duration: 2000,
      })
    })
    .finally(() => {
      loading.value = false
    })
}
</script>

<template>
  <view class="p-3 space-y-2">
    <!-- 商品信息展示 -->
    <view class="rounded-sm bg-white p-4">
      <view class="mb-1 text-xs text-gray-500">
        订单编号：{{ commentInfo.orderCode }}
      </view>
      <view class="flex space-x-3">
        <image
          v-if="commentInfo.goodsImage"
          class="h-20 w-20 rounded-sm"
          mode="aspectFill"
          :src="commentInfo.goodsImage"
        />
        <view v-else class="h-20 w-20 rounded-sm bg-gray-200" />
        <view class="flex-1">
          <view class="mb-1 font-medium">
            {{ commentInfo.goodsName }}
          </view>
          <view class="text-sm text-gray-500">
            {{ commentInfo.orderContent }}
          </view>
        </view>
      </view>
    </view>

    <!-- 评分 -->
    <view class="rounded-sm bg-white p-4">
      <view class="mb-3 text-base">
        商品评分
      </view>
      <up-rate
        v-model="commentForm.score"
        :active-color="Color.primary"
        size="24"
        gutter="4"
      />
    </view>

    <!-- 评价内容 -->
    <view class="rounded-sm bg-white p-4">
      <view class="mb-3 text-base">
        评价内容
      </view>
      <up-textarea
        v-model="commentForm.commentContent"
        placeholder="请分享您对商品的使用感受"
        :maxlength="200"
        :show-word-limit="true"
        :auto-height="true"
        border="surround"
        class="min-h-25 p-3"
      />
    </view>

    <!-- 图片上传 -->
    <view class="rounded-sm bg-white p-4">
      <view class="mb-3">
        <text class="text-base">
          上传图片
        </text>
        <text class="ml-2 text-sm text-gray-500">
          （可选，最多9张）
        </text>
      </view>

      <!-- 自定义图片上传组件 -->
      <view class="flex flex-wrap">
        <!-- 已上传的图片 -->
        <view
          v-for="(item, index) in commentImages"
          :key="index"
          class="relative m-2 h-20 w-20 overflow-hidden rounded-2"
        >
          <!-- 图片预览 -->
          <image
            :src="item.url"
            mode="aspectFill"
            class="h-full w-full object-cover"
            @tap="item.status === 'success' && previewImage(item.url)"
            @error="
              (e) => {
                console.error('图片加载失败:', item.url, e)
              }
            "
          />

          <!-- 图片状态指示器 -->
          <view
            v-if="item.status === 'uploading'"
            class="absolute left-0 top-0 h-full w-full center flex-col bg-black bg-opacity-50"
          >
            <view
              class="h-5 w-5 animate-spin border-2 border-white border-t-transparent rounded-full"
            />
            <text class="mt-2 text-xs text-white">
              上传中
            </text>
          </view>
          <view
            v-if="item.status === 'failed'"
            class="bg-red-500 absolute left-0 top-0 h-full w-full center flex-col bg-opacity-30"
          >
            <text class="mt-2 text-xs text-white">
              上传失败
            </text>
          </view>
          <!-- 删除按钮 -->
          <view
            class="absolute right-0 top-0 h-5 w-5 center rounded-full bg-black bg-opacity-70"
            @tap.stop="deleteCommentImage(index)"
          >
            <text class="text-base text-white leading-none">
              ×
            </text>
          </view>
        </view>

        <!-- 添加图片按钮 -->
        <view
          v-if="commentImages.length < maxImageCount"
          class="m-2 h-20 w-20 center flex-col border border-gray-300 rounded-2 border-dashed bg-gray-100"
          @tap="chooseImages"
        >
          <text class="text-2xl text-gray-400 leading-none">
            +
          </text>
          <text class="mt-2 text-xs text-gray-400">
            添加图片
          </text>
        </view>
      </view>

      <view class="mt-2 text-xs text-gray-500">
        支持jpg、png格式，单张图片不超过3MB
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="mt-4">
      <view
        class="center rounded p-3 text-white font-bold"
        :class="loading ? 'o-bg-primary-disable' : 'o-bg-primary'"
        @click="submitComment"
      >
        <up-loading-icon
          v-if="loading"
          mode="circle"
          color="white"
          size="20"
          class="mr-2"
        />
        {{ loading ? '提交中...' : '提交评价' }}
      </view>
    </view>

    <!-- 底部间距 -->
    <view class="p-8" />
  </view>
</template>
