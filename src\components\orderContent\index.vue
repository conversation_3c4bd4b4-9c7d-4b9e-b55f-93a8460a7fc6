<script lang="ts" setup>
import type {
  AllOrderPageResData,
} from '@/service/orderApi'
import PriceBox from '@/components/Price/PriceBox.vue'

defineProps<{
  data: AllOrderPageResData
  canSelect?: boolean
}>()

const emit = defineEmits<{
  'view-order-detail': [d: any]
}>()

function viewOrderDetail(orderPayCode: string) {
  emit('view-order-detail', orderPayCode)
}
</script>

<template>
  <view>
    <view class="flex items-center justify-between" @click="viewOrderDetail(data.orderPayCode)">
      <view :class="{ 'pl-8': canSelect }">
        <view class="o-color-aid text-xs">
          成交时间：{{ data.payDate }}
        </view>
        <view class="o-color-aid text-xs">
          订单编号：{{ data.orderCode }}
        </view>
      </view>
      <slot />
    </view>
    <view class="mt-2 flex space-x-2" @click="viewOrderDetail(data.orderPayCode)">
      <image
        v-if="data.otherOrderParamDTO?.goodsImage" class="f-img shrink-0" mode="aspectFill"
        :src="data.otherOrderParamDTO?.goodsImage"
      />
      <view v-else class="f-img shrink-0 bg-gray-200" />
      <view class="w-0 flex-1">
        <view class="truncate">
          {{ data.otherOrderParamDTO?.goodsName }}
        </view>
        <view class="break-all text-sm text-gray">
          {{ data.otherOrderParamDTO?.orderContent }}
        </view>
      </view>
    </view>
    <view class="flex items-end justify-between gap-3">
      <view />
      <view class="flex shrink-0 justify-end">
        <view class="flex items-baseline">
          <view class="text-sm">
            实付：
          </view>
          <price-box :price="data.actuallyPrice" :size="36" />
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-img {
  $w: 100rpx;
  width: $w;
  height: $w;
}
</style>
