<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "文章内容"
  }
}
</route>

<script lang="ts" setup>
import type { ArticleLoadRes } from '@/service/tutorialsApi'
import { PublicImgPath } from '@/components/image'
import { articleLoadApi } from '@/service/tutorialsApi'
import { getDeg } from '@/utils/tool'

const articleUrl = ref('')
const isUrl = ref(false)
const content = ref('')
const title = ref('')
const topImg = ref('')
const id = ref(0)

const { loading, error, data, run } = useRequest<ArticleLoadRes>(() =>
  articleLoadApi({
    articleId: id.value,
  }),
)

onLoad((option) => {
  id.value = Number(option.id)
  run().then(() => {
    if (data.value.articleUrl) {
      articleUrl.value = data.value.articleUrl
      isUrl.value = true
    }
    else {
      // 给视频适配宽度和高度
      const r = updateVideoDimensions(data.value.articleConent)
      // 添加默认行高
      content.value = addLineHeight(r)
      title.value = data.value.articleTitle
      topImg.value = data.value.imageUrl
    }
  })
})

// 使用正则表达式匹配 <body> 标签，并且替换为带有 style 属性的版本
function addLineHeight(articleContent: string) {
  return articleContent.replace(
    /<body(?![^>]*style=)/i, // 查找 <body 并确保后面没有 style 属性
    '<body style="line-height:1.5em;"', // 替换为带有 style 属性的 <body
  )
}

function updateVideoDimensions(articleContent: string) {
  // 正则表达式用于查找 <video> 标签，并捕获 width 和 height 属性
  const videoRegex = /<video[^>]*width="[^"]*"[^>]*height="[^"]*"[^>]*>/gi

  // 替换函数，它会将匹配到的 <video> 标签中的 width 和 height 更新为目标值
  const replaceFn = (match: string) => {
    return match
      .replace(/width="[^"]*"/, 'width="100%"')
      .replace(/height="[^"]*"/, 'height="200px"')
  }

  // 使用正则表达式和替换函数来更新 articleContent 中的所有 <video> 标签
  return articleContent.replace(videoRegex, replaceFn)
}
</script>

<template>
  <web-view v-if="isUrl" :src="articleUrl" />
  <view v-else class="f-page bg-white">
    <view v-if="loading" class="p-6">
      <up-skeleton rows="3" title loading :animate="true" />
    </view>
    <view v-else>
      <image v-if="topImg" class="w-full" mode="widthFix" :src="topImg" />
      <view
        v-else class="relative w-full center overflow-hidden py-8" :style="{
          '--f-deg': getDeg(title),
          'background': `linear-gradient(var(--f-deg), #ff5b00 3.23%, #ff5b00 3.24%, #ff9052 29.75%, #ffb162 56.27%, #ff9256 80.46%)`,
        }"
      >
        <view class="f-font relative z-2 w-full px-12 text-white font-bold">
          {{ title }}
        </view>
        <view class="f-vague absolute z-1">
          {{ title.substring(0, 3) }}
        </view>
      </view>
      <view class="px-6">
        <view class="px-6 pt-6 text-center text-xl font-bold" style="word-break: break-all; line-break: anywhere">
          {{ title }}
        </view>
        <up-divider />
        <up-parse :content="content" />
        <view class="f-divider mb-4 ml-auto mr-auto mt-16">
          <up-divider />
        </view>
        <view class="w-full center">
          <view class="f-app-logo flex items-center gap-1 rounded px-4 py-1">
            <image :src="`${PublicImgPath}logo-white.svg`" alt="迅码智联" mode="heightFix" style="height: 6vw" />
            <view class="f-tag-card h-fit rounded px-1 py-0.5 text-2xs text-white font-bold">
              智联
            </view>
          </view>
        </view>
        <view class="p-6" />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-page {
  min-height: 100vh;
}

.f-divider {
  width: 60%;
}

.f-app-logo {
  background: linear-gradient(90deg, #fa4c03 0%, #ff5b00 48.56%, #fd7424 100%);
}

.f-tag-card {
  background: linear-gradient(94deg, #181818 49.56%, #555 80.48%, #181818 98.01%);
}

.f-font {
  text-shadow: 0 6rpx 12rpx rgba(93, 3, 0, 0.46);
  font-size: 46rpx;
  line-height: 60rpx;
  text-align: center;
}

.f-vague {
  filter: blur(14rpx);
  color: #ff5900;
  font-size: 140rpx;
  font-weight: 900;
  right: -20rpx;
  bottom: -40rpx;
}
</style>
