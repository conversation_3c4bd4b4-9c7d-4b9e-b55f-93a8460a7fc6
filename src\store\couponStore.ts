import type {
  CouponPageResData,
} from '@/service/orderApi'
import { defineStore } from 'pinia'
import { ref } from 'vue'

const couponStore = defineStore('couponStore', () => {
  // 所选择的priceItem
  const bestPriceItem = ref<CouponPageResData>()
  const isSelectedCoupon = ref(false)
  function resetCouponStore() {
    bestPriceItem.value = undefined
    isSelectedCoupon.value = false
  }

  return {
    bestPriceItem,
    isSelectedCoupon,
    resetCouponStore,
  }
})

export const useCouponStore = couponStore()
