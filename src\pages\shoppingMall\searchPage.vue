<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "搜索"
  }
}
</route>

<script lang="ts" setup>
import type { MallGoodsPageRes } from '@/service/shoppingMallApi'
import { OrderDirection } from '@/enums/httpEnum'
import { mallGoodsPageApi } from '@/service/shoppingMallApi'

const goodsName = ref('')
const page = ref<number>(1)
const showEmptyIcon = ref(false)
const list = ref<any[]>([])

const { loading, error, data, run } = useRequest<MallGoodsPageRes>(() =>
  mallGoodsPageApi({
    goodsName: goodsName.value,
    groupBy: '',
    needTotalCount: true,
    orderBy: '',
    orderDirection: OrderDirection.asc,
    pageIndex: page.value,
    pageSize: 20,
  }),
)

function init() {
  return new Promise((resolve, reject) => {
    list.value = []
    page.value = 1
    showEmptyIcon.value = false
    run()
      .then(() => {
        list.value = [...data.value]
        resolve(true)
      })
      .catch((err) => {
        reject(err)
      })
  })
}

// 滚到页面底部加载更多
onReachBottom(() => {
  if (!showEmptyIcon.value) {
    page.value++
    run().then(() => {
      list.value = [...list.value, ...data.value]
      if (data.value.length === 0) {
        showEmptyIcon.value = true
      }
    })
  }
})

const scrollTop = ref<number>(0)
onPageScroll((e) => {
  scrollTop.value = e.scrollTop
})

function handleSearch() {
  init()
}
</script>

<template>
  <view class="sticky left-0 top-0 z-1 w-full">
    <view class="bg-white px-4 py-2">
      <view class="o-bg-no flex items-center gap-2 py-1 pl-4 pr-3" style="border-radius: 2rem">
        <up-input
          v-model="goodsName"
          border="none"
          class="o-bg-transparent grow"
          placeholder="搜索商品"
          clearable
          :maxlength="50"
        />
        <up-icon name="search" :size="20" @click="handleSearch" />
      </view>
    </view>
  </view>
  <view class="grid grid-cols-2 gap-3 px-3 pb-6 pt-3">
    <view
      v-for="item in list"
      :key="item.goodsId"
      class="flex flex-col items-center justify-center space-y-2"
    >
      <up-image
        class="overflow-hidden rounded"
        :width="85"
        :height="85"
        :src="item.imageUrl"
      />
      <view class="text-center text-sm">
        {{ item.goodsName }}
      </view>
    </view>
  </view>
  <view v-if="showEmptyIcon" class="o-color-aid w-full text-center">
    - 已经到底了 -
  </view>
  <up-back-top :scroll-top="scrollTop" />
</template>

<style lang="scss" scoped></style>
