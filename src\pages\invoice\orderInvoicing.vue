<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page -->
<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "订单开票",
    "enablePullDownRefresh": true,
    "backgroundColor": "#f0f3f8"
  }
}
</route>

<script lang="ts" setup>
import type { AllOrderPageRes, AllOrderPageResData } from '@/service/orderApi'
import { storeToRefs } from 'pinia'
import orderContent from '@/components/orderContent/index.vue'
import { InvoiceState, OrderStatus, PayState } from '@/enums'
import { Color } from '@/enums/colorEnum'
import { OrderDirection } from '@/enums/httpEnum'
import { allOrderPageApi } from '@/service/orderApi'
import { invoiceOrderStore } from '@/store/invoiceOrderStore'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()
const { userId } = storeToRefs(userStore)

const useInvoiceOrderStore = invoiceOrderStore()
const { orderObjList, orderSuccess } = storeToRefs(useInvoiceOrderStore)
const showEmptyIcon = ref(false)
const orderCode = ref([])
const page = ref(1)
const list = ref<AllOrderPageResData[]>([])
const filterList = [{ name: '全部' }, { name: '未开票' }, { name: '已开票' }]
const filterCurrent = ref(0)

// 下拉刷新
onPullDownRefresh(() => {
  showEmptyIcon.value = false
  page.value = 1
  init().finally(() => {
    uni.stopPullDownRefresh()
  })
})

function getIsInvoiced() {
  switch (filterCurrent.value) {
    case 0:
      return null
    case 1:
      return 0
    case 2:
      return 1
    default:
      return null
  }
}

const { loading, error, data, run } = useRequest<AllOrderPageRes>(() =>
  allOrderPageApi({
    orderStutas: [OrderStatus.waitingEvaluate, OrderStatus.done, OrderStatus.afterSale, OrderStatus.afterSaleEnd],
    isInvoiced: getIsInvoiced(),
    userId: userId.value,
    payState: PayState.paid,
    groupBy: '',
    needTotalCount: true,
    // orderBy: 'payDate',
    orderDirection: OrderDirection.desc,
    pageIndex: page.value,
    pageSize: 50,
  }),
)

// 滚到页面底部加载更多
onReachBottom(() => {
  if (!showEmptyIcon.value) {
    page.value++
    run().then(() => {
      list.value = [...list.value, ...data.value]
      if (data.value.length === 0) {
        showEmptyIcon.value = true
      }
    })
  }
})

function handleSelect(d: AllOrderPageResData) {
  if ([OrderStatus.waitingEvaluate, OrderStatus.done].includes(d.orderStutas) && (d.invoiceState === InvoiceState.notApply || d.invoiceState === null)) {
    if (orderCode.value.includes(d.orderCode)) {
      orderCode.value = orderCode.value.filter(item => item !== d.orderCode)
      // selectValue.value = selectValue.value.filter((item) => item.orderCode !== d.orderCode)
    }
    else {
      orderCode.value.push(d.orderCode)
      // selectValue.value.push(d)
    }
  }
}

function handleSubmit() {
  if (orderCode.value.length > 0) {
    const length = orderCode.value.length
    let price = 0
    const selectValue = list.value.filter(item => orderCode.value.includes(item.orderCode))
    selectValue.forEach((subItem) => {
      price += subItem.actuallyPrice
    })
    price = Number(price.toFixed(2))
    orderObjList.value = selectValue.map((item) => {
      return {
        orderId: item.orderId,
        serverType: item.serverType,
      }
    })
    uni.navigateTo({
      url: `/pages/invoice/invoiceInfo?length=${length}&price=${price}`,
    })
  }
}

function getStateColor(item: AllOrderPageResData) {
  switch (item.invoiceState) {
    case InvoiceState.failed:
      return 'color-red'
    case InvoiceState.applying:
      return 'color-primary'
    case InvoiceState.done:
      return 'color-green'
    default:
      return 'text-gray'
  }
}

function getDisable(d: AllOrderPageResData) {
  return !([OrderStatus.waitingEvaluate, OrderStatus.done].includes(d.orderStutas) && (d.invoiceState === InvoiceState.notApply || d.invoiceState === null))
}

function handleFilter() {
  page.value = 1
  init()
}

function init() {
  return new Promise((resolve, reject) => {
    list.value = []
    orderCode.value = []
    orderObjList.value = []
    run()
      .then(() => {
        list.value = [...data.value]
        resolve(true)
      })
      .catch((err) => {
        reject(err)
      })
  })
}

onLoad(() => {
  // 页面加载,清空orderCode，后退到此页面时，不触发onLoad
  init()
})

onShow(() => {
  // 订单成功后退触发
  if (orderSuccess.value) {
    orderSuccess.value = false
    page.value = 1
    init()
  }
})
// TODO 下拉刷新用scrollView
// TODO 检查所有页面，当<up-checkbox>没用v-model时，点击slot内容触发，有没有跟点击框，割裂不同步值
// TODO 每个提交功能，是否有做防止重复点击提交
</script>

<template>
  <up-sticky bg-color="#fff">
    <up-tabs
      v-model:current="filterCurrent" :line-color="Color.primary" :line-width="90" :scrollable="false"
      :list="filterList" @change="handleFilter"
    />
  </up-sticky>
  <view class="px-4 pb-10">
    <up-checkbox-group v-model="orderCode" placement="column">
      <view
        v-for="item in list" :key="`${item.serverType}${item.orderId}`" class="relative mt-3 rounded bg-white p-4"
        @click="handleSelect(item)"
      >
        <order-content :data="item" :can-select="true" @view-order-detail="() => { }">
          <view>
            <text v-if="item.orderStutas === OrderStatus.afterSale" class="text-right text-xs text-gray">
              售后中
            </text>
            <text v-else-if="item.orderStutas === OrderStatus.afterSaleEnd" class="text-right text-xs text-gray">
              售后单
            </text>
            <view v-if="item.invoiceStateStr !== '未申请'" class="font-bold" :class="getStateColor(item)">
              {{ item.invoiceStateStr }}
            </view>
          </view>
        </order-content>
        <up-checkbox
          :active-color="Color.primary" :name="item.orderCode" :disabled="getDisable(item)"
          class="absolute left-4 top-4"
        />
      </view>
    </up-checkbox-group>
    <up-empty v-if="list?.length === 0" icon="https://app.xunma.store/images/common/search.png" text="暂无订单" />
  </view>
  <view v-if="showEmptyIcon" class="o-color-aid w-full text-center">
    - 已经到底了 -
  </view>
  <view class="p-10" />
  <view class="fixed bottom-0 left-0 z-10 box-border w-full p-4">
    <view
      class="flex flex-grow-1 items-center justify-center rounded p-3 color-white font-bold"
      :class="orderCode.length > 0 ? 'o-bg-primary' : 'o-bg-primary-disable'" @click="handleSubmit"
    >
      {{ orderCode.length > 1 ? '合并开票' : '开票' }}
    </view>
  </view>
</template>

<style lang="scss" scoped></style>
