<script setup lang="ts">
const props = defineProps<{
  carousesList: string[]
  windowWidth: number
}>()

const current = ref(0)
// 获取胶囊按钮位置作为参考
const menuButtonInfo = wx.getMenuButtonBoundingClientRect()

function handleBack() {
  uni.navigateBack()
}

// 预览图片
function previewImage(index: number) {
  uni.previewImage({
    urls: props.carousesList,
    current: index,
  })
}
</script>

<template>
  <view class="bg-white">
    <view class="bg-primary" :style="{ paddingTop: `${menuButtonInfo.top + menuButtonInfo.height}px` }">
      <up-swiper
        v-model:current="current" :height="windowWidth" :list="carousesList" indicator :autoplay="false"
        indicator-style="right: 20px" @click="previewImage(current)"
      >
        <template #indicator>
          <view class="flex justify-center rd-6 px-3 py-1" style="background-color: rgba(0, 0, 0, 0.35)">
            <text class="text-xs color-white">
              {{ current + 1 }}/{{ carousesList.length }}
            </text>
          </view>
        </template>
      </up-swiper>
    </view>

    <view
      class="f-back fixed left-4 z-100 center rounded"
      :style="{ top: `${menuButtonInfo.top}px`, height: `${menuButtonInfo.height}px`, width: `${menuButtonInfo.height}px` }"
      @click="handleBack"
    >
      <up-icon color="#fff" class="ml--0.5" name="arrow-left" :size="14" />
    </view>
  </view>
</template>

<style scoped lang="scss">
.f-back {
  background-color: rgba(0, 0, 0, 0.25);
}
</style>
