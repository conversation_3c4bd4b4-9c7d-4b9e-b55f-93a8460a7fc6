<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "我的订单",
    "backgroundColor": "#f0f3f8"
  }
}
</route>

<script lang="ts" setup>
import type {
  AllOrderPageRes,
  AllOrderPageResData,
} from '@/service/orderApi'
import { storeToRefs } from 'pinia'
import orderContent from '@/components/orderContent/index.vue'
import { InvoiceState, OrderStatus, PayState } from '@/enums'
import { Color } from '@/enums/colorEnum'
import { OrderDirection } from '@/enums/httpEnum'
import {
  allOrderPageApi,
  confirmReceiptApi,
} from '@/service/orderApi'
import { useCommentStore } from '@/store/commentStore'
import { invoiceOrderStore } from '@/store/invoiceOrderStore'
import { useRefundStore } from '@/store/refundStore'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()
const { userId } = storeToRefs(userStore)

const useInvoiceOrderStore = invoiceOrderStore()
const { orderObjList } = storeToRefs(useInvoiceOrderStore)
const refundStore = useRefundStore()
const showEmptyIcon = ref(false)
const orderCode = ref([])
const page = ref(1)
const list = ref<AllOrderPageResData[]>([])
const tabsList = [{ name: '全部' }, { name: '待发货' }, { name: '待收货' }, { name: '售后' }]
const currentTab = ref(0)
const refresherTriggered = ref(false)
const loading = ref(false)

function getOrderList() {
  return new Promise((resolve, reject) => {
    loading.value = true
    allOrderPageApi({
      // serverType:
      // isInvoiced: getIsInvoiced(),
      orderStutas: getOrderStatus(),
      userId: userId.value,
      payState: PayState.paid,
      groupBy: '',
      needTotalCount: true,
      // orderBy: 'payDate',
      orderDirection: OrderDirection.desc,
      pageIndex: page.value,
      pageSize: 50,
    })
      .then((res: AllOrderPageRes) => {
        if (res.data.length === 0) {
          showEmptyIcon.value = true
        }
        else {
          showEmptyIcon.value = false
        }
        resolve(res)
      })
      .catch((err) => {
        reject(err)
      })
      .finally(() => {
        loading.value = false
        uni.hideLoading()
      })
  })
}

onLoad((options) => {
  // 页面加载,清空orderCode，后退到此页面时，不触发onLoad
  if (options.tab) {
    switch (options.tab) {
      case 'all':
        currentTab.value = 0
        break
      case 'waitSend':
        currentTab.value = 1
        break
      case 'waitReceive':
        currentTab.value = 2
        break
      case 'refund':
        currentTab.value = 3
        break
      default:
        currentTab.value = 0
        break
    }
  }
})

onShow(() => {
  // 清空数据
  list.value = []
  orderCode.value = []
  orderObjList.value = []
  getOrderList().then((res: AllOrderPageRes) => {
    list.value = res.data
  })
})

// scroll-view 下拉刷新
function onRefresh() {
  refresherTriggered.value = true
  showEmptyIcon.value = false
  page.value = 1
  // 清空数据
  list.value = []
  orderCode.value = []
  orderObjList.value = []
  getOrderList()
    .then((res: AllOrderPageRes) => {
      list.value = res.data
    })
    .catch(() => {
      uni.showToast({
        title: '刷新失败',
        icon: 'none',
      })
    })
    .finally(() => {
      refresherTriggered.value = false
    })
}

// scroll-view 上拉加载更多
function onLoadMore() {
  if (!showEmptyIcon.value && !loading.value) {
    page.value = page.value + 1
    getOrderList()
      .then((res: AllOrderPageRes) => {
        if (res && res.data.length > 0) {
          list.value = [...list.value, ...res.data]
        }
        else {
          // 没有更多数据
          showEmptyIcon.value = true
        }
      })
      .catch(() => {
        // 加载失败时回退页码
        page.value--
        uni.showToast({
          title: '加载更多失败',
          icon: 'none',
        })
      })
  }
}

function getOrderStatus() {
  switch (currentTab.value) {
    case 0:
      // all
      return []
    case 1:
      // waitSend
      return [OrderStatus.waitingSend]
    case 2:
      // waitReceive
      return [OrderStatus.waitingReceive]
    case 3:
      // refund
      return [OrderStatus.afterSale, OrderStatus.afterSaleEnd]
    default:
      return []
  }
}

function getOrderStatusColor(status: OrderStatus) {
  switch (status) {
    case OrderStatus.waitingSend:
      return 'text-orange'
    case OrderStatus.waitingReceive:
      return 'text-primary'
    case OrderStatus.waitingEvaluate:
      return 'text-primary'
    case OrderStatus.done:
      return 'text-primary'
    case OrderStatus.afterSale:
      return 'text-red'
    case OrderStatus.afterSaleEnd:
      return 'text-gray'
    default:
      return 'text-gray'
  }
}

function handleFilter() {
  page.value = 1
  // 清空数据
  list.value = []
  orderCode.value = []
  orderObjList.value = []
  getOrderList()
    .then((res: AllOrderPageRes) => {
      list.value = res.data
    })
    .catch(() => {
      uni.showToast({
        title: '获取数据失败',
        icon: 'none',
      })
    })
}

function handleViewLogistics(orderCode: string) {
  uni.navigateTo({
    url: `/pages/myOrder/logisticsDetail?orderCode=${orderCode}`,
  })
}

function viewOrderDetail(orderPayCode: string) {
  uni.navigateTo({
    url: `/pages/myOrder/orderDetail?orderPayCode=${orderPayCode}`,
  })
}

function handleConfirmReceipt(data: AllOrderPageResData) {
  const { transactionNo, orderCode } = data
  wx?.openBusinessView({
    businessType: 'weappOrderConfirm',
    extraData: {
      transaction_id: transactionNo,
    },
    success() {
      confirmReceiptApi({
        orderCode,
      })
        .then((res) => {
          if (res.success) {
            // 等待服务器回调微信支付成功的时间
            loading.value = true
            uni.showLoading({
              title: '加载中',
            })
            setTimeout(() => {
              getOrderList()
            }, 2000)
          }
        })
        .catch((err) => {
          uni.showToast({
            icon: 'error',
            title: err,
            duration: 3000,
          })
        })
    },
    fail() {
      uni.showToast({
        icon: 'error',
        title: '确认收货失败',
        duration: 3000,
      })
    },
  })
}

function handleApplyAfterSale(orderCode: string, item: AllOrderPageResData) {
  // 设置售后申请商品信息
  refundStore.setRefundGoodsInfo({
    orderCode,
    goodsName: item.otherOrderParamDTO?.goodsName,
    orderContent: item.otherOrderParamDTO?.orderContent,
    goodsImage: item.otherOrderParamDTO?.goodsImage,
    payDate: item.payDate,
  })
  uni.navigateTo({
    url: `/pages/myOrder/refundApply?orderCode=${orderCode}`,
  })
}

// 查看售后详情
function viewRefundDetail(refundOrderId: number) {
  uni.navigateTo({
    url: `/pages/myOrder/refundDetail?refundOrderId=${refundOrderId}`,
  })
}

// 打开评价页面
function openCommentModal(item: AllOrderPageResData) {
  const commentStore = useCommentStore()
  commentStore.setCommentInfo({
    orderId: item.orderId,
    orderCode: item.orderCode,
    goodsName: item.otherOrderParamDTO?.goodsName || '',
    goodsImage: item.otherOrderParamDTO?.goodsImage || '',
    orderContent: item.otherOrderParamDTO?.orderContent || '',
  })
  uni.navigateTo({
    url: '/pages/myOrder/commentOrder',
  })
}

function handleApplyInvoice() {
  uni.navigateTo({
    url: '/pages/invoice/orderInvoicing',
  })
}
</script>

<template>
  <up-sticky bg-color="#fff">
    <up-tabs
      v-model:current="currentTab" :line-color="Color.primary" :line-width="90" :scrollable="false"
      :list="tabsList" @change="handleFilter"
    />
  </up-sticky>
  <scroll-view
    :scroll-y="true" :refresher-enabled="true" :refresher-triggered="refresherTriggered"
    :style="{ height: 'calc(100vh - 44px)' }" @refresherrefresh="onRefresh" @scrolltolower="onLoadMore"
  >
    <div class="px-3">
      <view v-for="item in list" :key="`${item.serverType}${item.orderId}`" class="mt-2 rounded-sm bg-white p-3">
        <order-content :data="item" @view-order-detail="viewOrderDetail">
          <view class="font-bold" :class="getOrderStatusColor(item.orderStutas)">
            {{ item.orderStutasStr }}
          </view>
        </order-content>
        <view v-if="item.orderStutas !== OrderStatus.waitingSend" class="o-line mb-2 mt-2" />
        <view class="flex justify-end space-x-2">
          <view
            v-if="[OrderStatus.waitingEvaluate, OrderStatus.done].includes(item.orderStutas) && item.invoiceState === InvoiceState.notApply"
            class="center rounded-sm bg-gray-100 px-5 py-2 text-sm" @click="handleApplyInvoice"
          >
            申请开票
          </view>
          <view
            v-if="[OrderStatus.waitingReceive, OrderStatus.done].includes(item.orderStutas)"
            class="center rounded-sm bg-gray-100 px-5 py-2 text-sm" @click="handleViewLogistics(item.orderCode)"
          >
            查看物流
          </view>
          <view
            v-if="item.orderStutas === OrderStatus.waitingReceive"
            class="center rounded-sm bg-primary px-5 py-2 text-sm text-white" @click="handleConfirmReceipt(item)"
          >
            确认收货
          </view>
          <view
            v-if="[OrderStatus.waitingEvaluate, OrderStatus.done].includes(item.orderStutas)"
            class="center rounded-sm bg-gray-100 px-5 py-2 text-sm" @click="handleApplyAfterSale(item.orderCode, item)"
          >
            申请售后
          </view>
          <view
            v-if="[OrderStatus.afterSale, OrderStatus.afterSaleEnd].includes(item.orderStutas)"
            class="center rounded-sm bg-gray-100 px-5 py-2 text-sm" @click="viewRefundDetail(item.refundOrderId)"
          >
            售后详情
          </view>
          <view
            v-if="item.orderStutas === OrderStatus.waitingEvaluate"
            class="center rounded-sm bg-primary px-5 py-2 text-sm text-white" @click="openCommentModal(item)"
          >
            评价
          </view>
        </view>
      </view>
      <up-empty v-if="list?.length === 0" icon="https://app.xunma.store/images/common/search.png" text="暂无订单" />
    </div>
    <view v-if="showEmptyIcon" class="o-color-aid w-full pb-10 pt-4 text-center">
      - 已经到底了 -
    </view>
  </scroll-view>
</template>

<style lang="scss" scoped></style>
