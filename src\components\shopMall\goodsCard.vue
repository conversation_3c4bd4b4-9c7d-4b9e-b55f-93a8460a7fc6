<script setup lang="ts">
const props = defineProps<{
  data: any
}>()

function toContextPage(id: number) {
  uni.navigateTo({
    url: `/pages/tutorials/contextPage?id=${id}`,
  })
}
</script>

<template>
  <view class="mb-3 flex gap-1 rounded bg-white p-2 text-sm" @click="toContextPage(data.articleId)">
    <up-image
      class="shrink-0 overflow-hidden rounded"
      :width="85"
      :height="85"
      :src="data.imageUrl"
    />
    <view class="p-2">
      <view class="o-color-danger">
        {{ data.goodsName }}
      </view>
      <view>{{ data.goodsStatusName }}</view>
    </view>
  </view>
</template>

<style scoped lang="scss"></style>
