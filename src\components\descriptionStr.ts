import type { ServerType } from '@/enums'
import { Article, BarType, GoodsIdEnum } from '@/enums'
import { PublicImgPath } from './image'

const BASE_MAKE_STEP = [
  '输入7-9位厂商识别代码（可在《中国商品条码系统成员证书》上找到；或点击在输入框下的“查询厂商识别代码”后，输入公司名称查询）。',
  '输入3-5位项目代码（例如输入001~003，会生成001、002、003这三组代码）。建议一次性做200个享最大优惠。',
  '检验码会由系统生成，无需输入（如果您手上已有校验码也无需输入，自动生成的也会跟您手上的一样）。',
  '按需求选择放大系数（一般默认即可）。',
  '提交制作预览。核对订单无误后支付。',
  '等待系统制作订单（通常1秒内完成，量大时可关闭小程序，等待10分钟）。',
  '在我的条码中选择生成的条码，并下载。',
]
const BASE_CODED_COMPOSITION = [
  '7~9 位厂商识别代码（固定）',
  '3~5 位项目代码（厂商自行决定）',
  '1 位校验码（自动生成）',
]

export const BlowUpStr: string
  = '条码符号随放大系数的变化而放大或缩小，不同放大系数所对应的条码符号尺寸如表：'

export const makeFilmAndReportServiceDescription = '产品信息通报指导 + 条码符合性确认与条码查重指导'

interface ImageInfo {
  data: string
  height: number
  width: number
}

interface BlowUpInfo {
  coefficient: string
  w: string
  h: string
}

interface DescriptionItem {
  colorClass: string
  title: string
  typeStr: string
  description: string
  img?: ImageInfo
  sizeImg?: ImageInfo
  makeStep: string[]
  codedComposition: string[]
  blowUp?: BlowUpInfo[]
  designStep?: string[]
  policy: Article[]
}

type DescriptionStrKeys = keyof typeof ServerType | BarType | 'chargingStandard'

type DescriptionStr = Partial<{
  [K in DescriptionStrKeys]: K extends 'chargingStandard' ? string : DescriptionItem
}>

export const DESCRIPTION_STR: DescriptionStr = {
  EAN13: {
    colorClass: 'sf-title-box-blue',
    title: '单品条码',
    typeStr: '用于零售商品',
    description: 'EAN-13标准：主要用于零售商品印刷包装',
    img: {
      data: `${PublicImgPath}p_serve_desc_img_EAN13.png`,
      height: 88.16,
      width: 127.07,
    },
    sizeImg: {
      data: `${PublicImgPath}p_serve_desc_sizeImg_EAN13.png`,
      height: 104,
      width: 141.13,
    },
    makeStep: BASE_MAKE_STEP,
    codedComposition: BASE_CODED_COMPOSITION,
    policy: [Article.film, Article.platform],
    blowUp: [
      {
        coefficient: '0.80',
        w: '29.83',
        h: '20.74',
      },
      {
        coefficient: '0.85',
        w: '31.70',
        h: '22.04',
      },
      {
        coefficient: '0.90',
        w: '33.56',
        h: '23.34',
      },
      {
        coefficient: '1.00',
        w: '37.29',
        h: '25.93',
      },
      {
        coefficient: '1.10',
        w: '41.01',
        h: '28.52',
      },
      {
        coefficient: '1.20',
        w: '44.75',
        h: '31.12',
      },
      {
        coefficient: '1.30',
        w: '48.48',
        h: '33.71',
      },
      {
        coefficient: '1.40',
        w: '52.21',
        h: '36.30',
      },
      {
        coefficient: '1.50',
        w: '55.94',
        h: '38.90',
      },
      {
        coefficient: '1.60',
        w: '59.66',
        h: '41.49',
      },
      {
        coefficient: '1.70',
        w: '63.39',
        h: '44.08',
      },
      {
        coefficient: '1.80',
        w: '67.12',
        h: '46.67',
      },
      {
        coefficient: '1.90',
        w: '70.85',
        h: '49.27',
      },
      {
        coefficient: '2.00',
        w: '74.58',
        h: '51.86',
      },
    ],
  },
  ITF14: {
    colorClass: 'sf-title-box-blue',
    title: '外箱条码',
    typeStr: '用于储运包装',
    description: 'ITF-14标准：用于储运包装非零售包装/箱码',
    img: {
      data: `${PublicImgPath}p_serve_desc_img_ITF14.png`,
      height: 53.01,
      width: 158.89,
    },
    sizeImg: {
      data: `${PublicImgPath}p_serve_desc_sizeImg_ITF14.png`,
      height: 67,
      width: 172,
    },
    makeStep: ['输入1位包装指示符（1~8为定量储运包装，9为变量储运包装）。', ...BASE_MAKE_STEP],
    codedComposition: ['1 位包装指示符（1~9）', ...BASE_CODED_COMPOSITION],
    policy: [Article.film, Article.platform],
    blowUp: [
      {
        coefficient: '0.50',
        w: '82.25',
        h: '50.00',
      },
      {
        coefficient: '0.60',
        w: '96.30',
        h: '50.00',
      },
      {
        coefficient: '0.70',
        w: '110.35',
        h: '50.00',
      },
      {
        coefficient: '0.80',
        w: '124.4',
        h: '50.00',
      },
      {
        coefficient: '0.90',
        w: '138.45',
        h: '50.00',
      },
      {
        coefficient: '1.00',
        w: '152.5',
        h: '50.00',
      },
    ],
  },
  storeCode: {
    colorClass: 'sf-title-box-blue',
    title: '店内条码',
    typeStr: '用于店内自行加工商品',
    description: '无需厂商识别代码也可制作',
    makeStep: [
      '选择前缀20~24。',
      '输入自定义的10位项目代码。',
      '提交制作预览。核对订单无误后支付。',
      '等待系统制作订单（通常1秒内完成，量大时可关闭小程序，等待10分钟）。',
      '在我的条码中选择生成的条码，并下载。',
    ],
    codedComposition: BASE_CODED_COMPOSITION,
    policy: [Article.film, Article.platform],
  },
  infoReport: {
    colorClass: 'sf-title-box-blue',
    title: '产品编码信息通报',
    typeStr: '产品通报',
    description: '报备完成后扫码才会有信息',
    // '按需求选择通报合适的通报服务。',
    // '完成付款后，用电脑打开 www.gs1helper.com 网站，并点击右上角登录按钮。',
    // '完成登录后，点击页面上方 信息通报。',
    // '根据信息通报>批量上传，显示的步骤完成信息上传。',
    makeStep: [],
    codedComposition: BASE_CODED_COMPOSITION,
    policy: [Article.infoReport, Article.platform, Article.platform],
  },
  importedGoods: {
    colorClass: 'sf-title-box-blue',
    title: '进口商品报备',
    typeStr: '进口商品报备服务',
    description: '进口商品报备，助力通关',
    makeStep: [
      '如果已经是中国商品条码成员，购买服务后，联系客服，提供相关产品资料即可办理。',
      '如果是非中国商品条码成员的，购买服务后，联系客服，除了提供相关产品资料，还需额外提供营业执照（盖章）跟企业认证注册登记表（签字盖章）。',
    ],
    codedComposition: BASE_CODED_COMPOSITION,
    policy: [Article.infoReport, Article.platform, Article.platform],
  },
  miniShop: {
    colorClass: 'sf-title-box-blue',
    title: '二维码微站',
    typeStr: '二维码',
    description: '扫码展示更多详情与商品',
    makeStep: [
      // '开通二维码微站',
      // '无限量生成商品二维码、企业二维码。',
      // '扫码展示更丰富的商品信息，例如说明书下载、在线视频安装教程。',
      // '扫码展示企业全部商品，方便客户选型选品。',
      // '快速构建企业移动门户，助力企业品牌构建。',
    ],
    codedComposition: BASE_CODED_COMPOSITION,
    policy: [Article.miniShop, Article.platform, Article.platform],
  },
  labelPrint: {
    colorClass: 'sf-title-box-yellow',
    title: '标签印刷',
    typeStr: '标签印刷',
    description: '合规范的标签印刷',
    makeStep: [
      '选择材质、尺寸、数量。',
      '付款后，给客服提供相关需要打印的条码及文字信息、收件地址信息。',
      '设计师当天排版好标签设计稿，给您确认无误后，当天印刷发出。',
      '等待2-3日后收件。',
    ],
    codedComposition: BASE_CODED_COMPOSITION,
    policy: [Article.labelPrint, Article.platform],
  },
  designServer: {
    colorClass: 'sf-title-box-blue',
    title: '标签设计/规范指导/包装设计',
    typeStr: '专业设计服务',
    description: '交付合规设计文件，直接用于印刷',
    makeStep: [
      '先与客服沟通相关费用，对接设计师。',
      '发您的设计稿件给给您服务的设计师。',
      '设计师会根据您的行业，为您提供基本的合规化检查并告知不合规的设计处，或直接在稿件上修改。',
    ],
    designStep: [
      '先与客服沟通相关费用，对接设计师。',
      '先付一半的设计费，设计师开始设计并沟通修改。',
      '设计完成后，再给另一半设计费，收取设计源文件。',
    ],
    codedComposition: BASE_CODED_COMPOSITION,
    policy: [Article.design, Article.platform],
  },
  registerService: {
    colorClass: 'sf-title-box-blue',
    title: '条码注册申请',
    typeStr: '代理业务',
    description: '中国商品条码系统成员申请',
    makeStep: [],
    codedComposition: BASE_CODED_COMPOSITION,
    policy: [Article.agencyService, Article.platform],
  },
  renewalService: {
    colorClass: 'sf-title-box-blue',
    title: '条码续约续展',
    typeStr: '代理业务',
    description: '中国商品条码系统成员续期',
    makeStep: [],
    codedComposition: BASE_CODED_COMPOSITION,
    policy: [Article.agencyService, Article.platform],
  },
  modifyService: {
    colorClass: 'sf-title-box-blue',
    title: '条码成员信息变更',
    typeStr: '代理业务',
    description: '企业名称、注册地址、法人、企业类型变更',
    makeStep: [],
    codedComposition: BASE_CODED_COMPOSITION,
    policy: [Article.agencyService, Article.platform],
  },
  chargingStandard:
    '　　依据物编中心（2015）51号《中国物品编码中心关于贯彻落实国家发改委放开部分检验检测经营服务性收费的通知》条码研制费为每张32元。',
}

export interface BaseProjectType {
  descriptionServiceType: keyof typeof ServerType | BarType | 'agencyService'
  tagBgColor: string
  tagStrColor: string
  image: string
  title: string
  typeStr: string
  description: string
  url: string
}

type ProjectTypeLocal = BaseProjectType & {
  getInfoFrom: 'local'
}

type ProjectTypeShopMall = BaseProjectType & {
  getInfoFrom: 'shopMall'
  goodsId: number // 当 getInfoFrom 为 'shopMall' 时，goodsId 是必填项
}

export type ProjectType = ProjectTypeLocal | ProjectTypeShopMall

export const PROJECT_1: ProjectType[] = [
  {
    descriptionServiceType: 'labelPrint',
    tagBgColor: '#FFEEDE',
    tagStrColor: '#FF7D00',
    image: `${PublicImgPath}p_index_labelPrint.png`,
    title: '标签印刷',
    typeStr: DESCRIPTION_STR.labelPrint.typeStr,
    description: DESCRIPTION_STR.labelPrint.description,
    url: '/pages/labelPrint/index',
    getInfoFrom: 'local',
  },
  {
    descriptionServiceType: BarType.EAN13,
    tagBgColor: '#e8efff',
    tagStrColor: '#FF5B00',
    image: `${PublicImgPath}p_project_list_image_EAN13.png`,
    title: '条码制作',
    typeStr: DESCRIPTION_STR.EAN13.typeStr,
    description: '用于零售商品包装',
    url: '/pages/makeFilm/index',
    getInfoFrom: 'local',
  },
  {
    descriptionServiceType: 'infoReport',
    tagBgColor: '#e8efff',
    tagStrColor: '#FF5B00',
    image: `${PublicImgPath}p_index_Information_upload.png`,
    title: '产品信息通报',
    typeStr: DESCRIPTION_STR.infoReport.typeStr,
    description: '扫条形码没信息？',
    url: '/pages/infoReport/index',
    getInfoFrom: 'local',
  },
]

export const PROJECT_2: ProjectType[] = [
  {
    descriptionServiceType: BarType.ITF14,
    tagBgColor: '#e8efff',
    tagStrColor: '#FF5B00',
    image: `${PublicImgPath}p_project_list_image_ITF14.png`,
    title: '箱码制作',
    typeStr: DESCRIPTION_STR.ITF14.typeStr,
    description: '储运包装/箱码',
    url: '/pages/makeFilm/index',
    getInfoFrom: 'local',
  },
  {
    descriptionServiceType: 'agencyService',
    tagBgColor: '#e8efff',
    tagStrColor: '#FF5B00',
    image: `${PublicImgPath}p_index_register.png`,
    title: '注册续展变更',
    typeStr: '',
    description: '条码服务办理',
    url: '/pages/agencyService/index',
    getInfoFrom: 'local',
  },
  {
    // descriptionServiceType在paymentSuccess/makeFilm.vue有引用，改名时注意
    descriptionServiceType: 'designServer',
    tagBgColor: '#e8efff',
    tagStrColor: '#FF5B00',
    image: `${PublicImgPath}p_index_design.png`,
    // title: '规范指导/包装设计',
    title: '包装设计',
    typeStr: DESCRIPTION_STR.designServer.typeStr,
    description: '规范指导避免受罚',
    url: '/pages/designServer/index',
    getInfoFrom: 'local',
  },
  /*   {
    descriptionServiceType: 'miniShop',
    tagBgColor: '#e8efff',
    tagStrColor: '#FF5B00',
    image: PublicImgPath + 'p_index_shop.png',
    title: '二维码微站',
    typeStr: DESCRIPTION_STR.miniShop.typeStr,
    description: '扫码展示更多详情',
    // description: '扫码：产品目录+产品详情',
    url: '/pages/miniShop/index',
    getInfoFrom: 'local',
  }, */
  {
    descriptionServiceType: 'storeCode',
    tagBgColor: '#e8efff',
    tagStrColor: '#FF5B00',
    image: `${PublicImgPath}p_index_StoreCode.png`,
    title: '店内条码制作',
    typeStr: DESCRIPTION_STR.storeCode.typeStr,
    description: '无需厂商识别代码',
    url: '/pages/storeCode/index',
    getInfoFrom: 'local',
  },
  {
    descriptionServiceType: 'importedGoods',
    tagBgColor: '#e8efff',
    tagStrColor: '#FF5B00',
    image: `${PublicImgPath}p_index_importedGoods.png`,
    title: '进口商品报备',
    typeStr: DESCRIPTION_STR.importedGoods.typeStr,
    description: '进口商品报备服务',
    url: '/pages/importedGoods/index',
    getInfoFrom: 'local',
  },
  {
    descriptionServiceType: 'labelPrint',
    tagBgColor: '#e8efff',
    tagStrColor: '#FF5B00',
    image: `${PublicImgPath}p_index_labelPrint.png`,
    title: '标签印刷',
    typeStr: DESCRIPTION_STR.labelPrint.typeStr,
    description: '合规范的标签印刷',
    url: '/pages/shoppingMall/index',
    getInfoFrom: 'shopMall',
    goodsId: GoodsIdEnum.labelPrint,
  },
]

export const PROJECT_ALL: ProjectType[] = [...PROJECT_1, ...PROJECT_2]

// src/pages/myAgency/index.vue 有引用，改名时注意
export const PROJECT_3: ProjectType[] = [
  {
    descriptionServiceType: 'registerService',
    tagBgColor: '#e8efff',
    tagStrColor: '#FF5B00',
    image: `${PublicImgPath}p_index_register.png`,
    title: DESCRIPTION_STR.registerService.title,
    typeStr: DESCRIPTION_STR.registerService.typeStr,
    description: DESCRIPTION_STR.registerService.description,
    url: '/pages/agencyService/registerService',
    getInfoFrom: 'local',
  },
  {
    descriptionServiceType: 'renewalService',
    tagBgColor: '#FFEEDE',
    tagStrColor: '#FF7D00',
    image: `${PublicImgPath}p_index_register.png`,
    title: DESCRIPTION_STR.renewalService.title,
    typeStr: DESCRIPTION_STR.renewalService.typeStr,
    description: DESCRIPTION_STR.renewalService.description,
    url: '/pages/agencyService/renewalService',
    getInfoFrom: 'local',
  },
  {
    descriptionServiceType: 'modifyService',
    tagBgColor: '#e8efff',
    tagStrColor: '#FF5B00',
    image: `${PublicImgPath}p_index_register.png`,
    title: DESCRIPTION_STR.modifyService.title,
    typeStr: DESCRIPTION_STR.modifyService.typeStr,
    description: DESCRIPTION_STR.modifyService.description,
    url: '/pages/agencyService/modifyService',
    getInfoFrom: 'local',
  },
]

export const mainSale = [
  {
    imageUrl: `${PublicImgPath}index/mainImg_01.png`,
    goodsId: 22,
  },
  {
    imageUrl: `${PublicImgPath}index/2025Top1.jpg`,
    goodsId: 24,
  },
  { imageUrl: `${PublicImgPath}index/2024Top1.jpg`, goodsId: 0 },
  {
    imageUrl: `${PublicImgPath}index/2023Top1.jpg`,
    goodsId: 2,
  },
]
