<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "支付成功",
    "backgroundColor": "#f0f3f8"
  }
}
</route>

<script lang="ts" setup>
import { Color } from '@/enums/colorEnum'

const orderCode = ref('')

onLoad((option) => {
  if (option?.orderCode) {
    orderCode.value = option.orderCode
  }
})

// 返回首页或指定页面
function goBack() {
  uni.switchTab({
    url: '/pages/index/index',
  })
}

// 查看订单
function goToOrder() {
  uni.switchTab({
    // 这样才有后退打底
    url: '/pages/userPage/index',
    success: () => {
      uni.navigateTo({
        url: '/pages/myOrder/index',
      })
    },
  })
}
</script>

<template>
  <view class="min-h-screen flex flex-col items-center bg-white px-5 py-10">
    <!-- 成功图标 -->
    <view class="mb-6 mt-15">
      <up-icon name="checkmark-circle" :color="Color.primary" :size="80" />
    </view>

    <!-- 成功信息 -->
    <view class="mb-10 w-full text-center">
      <view class="mb-4 text-2xl text-primary font-bold">
        支付成功
      </view>
      <view v-if="orderCode" class="text-sm text-gray">
        订单编号：{{ orderCode }}
      </view>
    </view>

    <!-- 分割线 -->
    <view class="my-5 h-px w-full bg-gray-100" />

    <!-- 操作按钮 -->
    <view class="mt-6 w-full flex justify-center space-x-4">
      <view
        class="flex items-center justify-center rounded-sm bg-gray-100 px-5 py-2"
        @click="goBack"
      >
        返回首页
      </view>
      <view
        class="flex items-center justify-center rounded-sm bg-primary px-5 py-2 text-white"
        @click="goToOrder"
      >
        查看订单
      </view>
    </view>
  </view>
</template>
