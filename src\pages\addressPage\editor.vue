<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "收货地址编辑"
  }
}
</route>

<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { useAddressInfoHook } from '@/components/addressPage/addressInfoHook'
import PaidDisclaimer from '@/components/agreement/paidDisclaimer.vue'
import { Article, getArticleName } from '@/enums'
import { Color } from '@/enums/colorEnum'
import { addressCreateApi, addressUpdateApi } from '@/service/addressPageApi'
import { addressStore } from '@/store/addressStore'
import { msgModalStore } from '@/store/msgModalStore'
import { useUserStore } from '@/store/user'

const useMsgModalStore = msgModalStore()

const userStore = useUserStore()
const { userId } = storeToRefs(userStore)
const useAddressStore = addressStore()
const { addressData, addressId, selectAddressId } = storeToRefs(useAddressStore)

const operationType = ref('add')
const agree = ref(false)
const isModalShow = ref(false)

const { model, form, rules, validatePhoneNumber } = useAddressInfoHook()

onLoad((option: any) => {
  operationType.value = option.type
  if (option.type === 'edit') {
    model.addressDetail = addressData.value.addressDetail
    model.district = addressData.value.district
    model.isDefault = addressData.value.isDefault
    model.phone = addressData.value.phone
    model.realName = addressData.value.realName
  }
})

function toBack() {
  uni.showToast({
    title: '保存成功',
  })
  setTimeout(() => {
    uni.navigateBack()
  }, 1000)
}

function handleSubmit() {
  form.value
    .validate()
    .then((valid) => {
      console.log('valid', valid)
      if (valid) {
        if (agree.value) {
          switch (operationType.value) {
            case 'add':
              addressCreateApi({
                addressDetail: model.addressDetail,
                district: model.district,
                isDefault: model.isDefault,
                phone: model.phone,
                realName: model.realName,
                userId: userId.value,
              }).then(() => {
                toBack()
              })
              break
            case 'edit':
              addressUpdateApi({
                addressDetail: model.addressDetail,
                district: model.district,
                isDefault: model.isDefault,
                phone: model.phone,
                realName: model.realName,
                userId: userId.value,
                addressId: addressId.value,
              }).then(() => {
                toBack()
              })
              break
          }
        }
        else {
          useMsgModalStore
            .confirm({
              title: '温馨提示',
              content: `请先勾选已阅读并同意${getArticleName(Article.platform)}`,
            })
            .then(() => {
              uni.pageScrollTo({
                selector: '#agreeElement',
              })
            })
        }
      }
    })
    .catch((error) => {
      console.log(error, 'error')
    })
}

const addressInfo = ref({
  address: '',
  latitude: 0,
  longitude: 0,
})

function selectAddress() {
  // 点击调起地图选择位置
  uni.authorize({
    scope: 'scope.userLocation',
    success(res) {
      console.log('scope.userLocation获得授权', res)
      // 选择位置
      uni.chooseLocation({
        success(res) {
          console.log('选择地点成功', res)
          console.log(`位置名称：${res.name}`)
          console.log(`详细地址：${res.address}`)
          console.log(`纬度：${res.latitude}`)
          console.log(`经度：${res.longitude}`)
          addressInfo.value.address = res.address
          addressInfo.value.latitude = res.latitude
          addressInfo.value.longitude = res.longitude
        },
        fail(error) {
          console.log('选择位置失败', error)
        },
      })
    },
  })
}

function handleModalOk() {
  agree.value = true
  isModalShow.value = false
}
</script>

<template>
  <up-modal
    :show="isModalShow"
    confirm-text="同意"
    show-cancel-button
    close-on-click-overlay
    @confirm="handleModalOk"
    @cancel="isModalShow = false"
    @close="isModalShow = false"
  >
    <PaidDisclaimer :article-ids="[Article.platform]" />
  </up-modal>
  <view class="p-4 pb-6">
    <!--    <view class="uni-form-item uni-column address address-arrow">
      <view class="title">地址</view>
      <view class="operation" @click="selectAddress">
        <input
          v-model="addressInfo.address"
          class="uni-input"
          name="address"
          placeholder="请选择地址   "
          placeholder-class="address-pla"
        />
      </view>
    </view> -->
    <view class="overflow-hidden rounded bg-white pl-4">
      <up-form ref="form" label-width="100" class="mt-2" :model="model" :rules="rules">
        <up-form-item label="省市区" prop="district" required>
          <up-input v-model="model.district" border="bottom" clearable placeholder="省市区" />
        </up-form-item>
        <up-form-item label="详细地址与门牌号" prop="addressDetail" required>
          <up-input
            v-model="model.addressDetail"
            border="bottom"
            clearable
            placeholder="详细地址与门牌号"
          />
        </up-form-item>
        <up-form-item label="姓名" prop="realName" required>
          <up-input v-model="model.realName" border="bottom" clearable placeholder="姓名" />
        </up-form-item>
        <up-form-item label="联系手机号" prop="phone" required>
          <up-input
            v-model="model.phone"
            border="bottom"
            type="number"
            :maxlength="11"
            clearable
            placeholder="联系手机号"
          >
            <template #suffix>
              <text class="text-xs text-gray">
                {{ model.phone.length }}/11
              </text>
            </template>
          </up-input>
        </up-form-item>
        <view class="flex items-center justify-between p-4">
          <view class="text-sm">
            设为默认
          </view>
          <up-switch v-model="model.isDefault" :active-color="Color.primary" />
        </view>
      </up-form>
    </view>
  </view>
  <view id="agreeElement" class="flex items-center justify-center text-xs">
    <up-checkbox
      v-model:checked="agree"
      used-alone
      label-size="12"
      :size="14"
      :active-color="Color.primary"
      label="我已阅读并同意"
    />
    <view class="o-color-primary" @click.stop="isModalShow = true">
      {{ getArticleName(Article.platform) }}
    </view>
  </view>
  <view class="fixed bottom-0 left-0 z-10 box-border w-full p-4">
    <view
      class="o-bg-primary flex flex-grow-1 items-center justify-center rounded p-3 color-white font-bold"
      @click="handleSubmit"
    >
      保存
    </view>
  </view>
</template>

<style lang="scss" scoped></style>
