<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "商品详情",
    "navigationStyle": "custom",
    "navigationBarTextStyle": "white"
  },
}
</route>

<script lang="ts" setup>
import type {
  CouponPageResData,
} from '@/service/orderApi'
import type {
  MallCommentRecordPageRes,
  MallGoodsLoadRes,
  OrderCreateParams,
  SkuListData,
} from '@/service/shoppingMallApi'
import { storeToRefs } from 'pinia'
import CommentItem from '@/components/commentItem.vue'
import CsBtnLeftButton from '@/components/customer/csBtnLeftButton.vue'
import MallSwiper from '@/components/mallSwiper.vue'
import { wxRequestPayment } from '@/components/mix'
import PriceBox from '@/components/Price/PriceBox.vue'
import { CouponType, FromPlatform, GoodsStatus, ServerType } from '@/enums'
import { Color } from '@/enums/colorEnum'
import { OrderDirection } from '@/enums/httpEnum'
import { addressPageApi } from '@/service/addressPageApi'

import {
  couponPageApi,
} from '@/service/orderApi'
import {
  mallCommentRecordPageApi,
  mallGoodsLoadApi,
  orderCreateApi,
  payMallOrderApi,
  sumbitMallOrder,
} from '@/service/shoppingMallApi'
import { useCouponStore } from '@/store/couponStore'
import { msgModalStore } from '@/store/msgModalStore'
import { useUserStore } from '@/store/user'

// 获取屏幕边界到安全区域距离
const { safeAreaInsets, statusBarHeight, windowWidth, windowHeight } = uni.getSystemInfoSync()
const { bestPriceItem, isSelectedCoupon } = storeToRefs(useCouponStore)

const useMsgModalStore = msgModalStore()
const userStore = useUserStore()
const { userId } = storeToRefs(userStore)
const couponList = ref<CouponPageResData[]>([])
const loading = ref(false)
const mallData = ref<MallGoodsLoadRes['data']>()
const carousesList = ref<string[]>([]) // 主图 + 轮播图
const selectArr = ref<string[]>([])
const isSheetShow = ref(false)
const isShowPreviewImg = ref(false)
const quantity = ref(1)
const previewImgUrl = ref<string>()
const allGoodsImg = ref<string[]>([])
const imgCurrent = ref(0)
const couponPrice = ref(0)
const activeSkuItem = ref<SkuListData>()
let goodsId = 0

// 评价相关数据
const commentList = ref<MallCommentRecordPageRes['data']>([])
const commentTotal = ref(0)
const commentLoading = ref(false)

// 评论弹窗相关
const isCommentSheetShow = ref(false)
const allCommentList = ref<MallCommentRecordPageRes['data']>([])
const allCommentLoading = ref(false)
const allCommentPage = ref(1)
const allCommentPageSize = ref(10)
const hasMoreAllComment = ref(true)

const addressData = reactive({
  addressDetail: '',
  district: '',
  phone: '',
  realName: '',
})

const needPayPrice = computed(() => {
  let p = Number.parseFloat(((activeSkuItem.value?.originalPrice * quantity.value * 100 - couponPrice.value * 100) / 100).toFixed(2))
  if (p < 0) {
    p = 0
  }
  return p
})

// 获取评价列表（默认只显示前2个）
function getCommentList() {
  if (commentLoading.value)
    return

  commentLoading.value = true
  mallCommentRecordPageApi({
    goodsId,
    pageIndex: 1,
    pageSize: 2, // 只获取前2个评论
    orderDirection: OrderDirection.desc,
    orderBy: 'commentTime',
    needTotalCount: true,
  })
    .then((res) => {
      commentList.value = res.data
      commentTotal.value = res.totalCount
    })
    .finally(() => {
      commentLoading.value = false
    })
}

// 显示全部评论弹窗
function showAllComments() {
  isCommentSheetShow.value = true
  // 如果还没有加载过全部评论，则加载第一页
  if (allCommentList.value.length === 0) {
    getAllCommentList()
  }
}

// 获取全部评论列表
function getAllCommentList() {
  if (allCommentLoading.value || !hasMoreAllComment.value)
    return

  allCommentLoading.value = true
  mallCommentRecordPageApi({
    goodsId,
    pageIndex: allCommentPage.value,
    pageSize: allCommentPageSize.value,
    orderDirection: OrderDirection.desc,
    orderBy: 'commentTime',
    needTotalCount: true,
  })
    .then((res) => {
      if (allCommentPage.value === 1) {
        allCommentList.value = res.data
      }
      else {
        allCommentList.value.push(...res.data)
      }
      hasMoreAllComment.value = allCommentPage.value * allCommentPageSize.value < res.totalCount
      allCommentPage.value++
    })
    .finally(() => {
      allCommentLoading.value = false
    })
}

// 关闭评论弹窗
function closeCommentSheet() {
  isCommentSheetShow.value = false
}

// 滚动到底部加载更多评论
function onCommentScrolltolower() {
  if (hasMoreAllComment.value && !allCommentLoading.value) {
    getAllCommentList()
  }
}

// 预览评价图片
function previewCommentImage(images: string[], index: number) {
  uni.previewImage({
    urls: images,
    current: images[index],
  })
}

function getAddressData() {
  addressPageApi({
    userId: userStore.userId,
  }).then((res) => {
    if (res.data && res.data.length > 0) {
      const d = res.data.filter(item => item.isDefault)
      if (d.length > 0) {
        addressData.addressDetail = d[0].addressDetail
        addressData.district = d[0].district
        addressData.phone = d[0].phone
        addressData.realName = d[0].realName
      }
      else {
        addressData.addressDetail = res.data[0].addressDetail
        addressData.district = res.data[0].district
        addressData.phone = res.data[0].phone
        addressData.realName = res.data[0].realName
      }
    }
  })
}

// TODO 测试库存为0情况
function handleSubmit() {
  if (loading.value || activeSkuItem.value?.stock === 0) {
    return
  }

  const okFunc = () => {
    loading.value = false
    uni.hideLoading()
  }

  if (addressData.phone) {
    loading.value = true
    uni.showLoading({ title: '提交订单中...' })
    const params: OrderCreateParams = {
      attrDetailValueId: activeSkuItem.value.attrDetailValueId,
      goodsId,
      number: quantity.value,
      serverType: ServerType.shopMall,
      userId: useUserStore().userId,
    }
    params.fromTo = FromPlatform.wx
    orderCreateApi(params)
      .then((orderCodeRes) => {
        console.log('res', orderCodeRes)
        sumbitMallOrder({
          addressDetail: addressData.addressDetail,
          contact: addressData.realName,
          contactPhone: addressData.phone,
          district: addressData.district,
          couponId: bestPriceItem.value.couponId,
          // email: '',
          orderCode: orderCodeRes.data.orderCode,
        })
          .then((submitRes) => {
            payMallOrderApi({ orderCode: submitRes.data.orderCode })
              .then((payRes) => {
                const url = `/pages/paymentSuccess/index?orderCode=${submitRes.data.orderCode}`
                if (payRes.data.isNeedToPay) {
                  wxRequestPayment(payRes.data)
                    .then(() => {
                      okFunc()
                      // 跳转到支付成功页面，并传递订单编号和总价
                      uni.redirectTo({
                        url,
                      })
                    })
                    .catch(() => {
                      okFunc()
                    })
                }
                else {
                  uni.redirectTo({
                    url,
                  })
                }
              })
              .catch(() => {
                okFunc()
              })
          })
          .catch(() => {
            okFunc()
          })
      })
      .catch(() => {
        okFunc()
      })
  }
  else {
    useMsgModalStore
      .alert({
        title: '温馨提示',
        content: '请先添加地址',
      })
      .then(() => {
        handleAddressClick()
      })
  }
}

function isSelectTag(index: number, specValue: string) {
  if (selectArr.value.length === 0) {
    return false
  }
  return selectArr.value[index] === specValue
}

function handleSelectTag(index: number, specValue: string, length: number) {
  if (selectArr.value.length === 0) {
    for (let i = 0; i < length; i++) {
      selectArr.value.push('')
    }
  }
  selectArr.value[index] = specValue
  imgCurrent.value = mallData.value.skuList?.findIndex(
    item => item.specValueNameStr === selectArr.value.join(','),
  )
  activeSkuItem.value = mallData.value.skuList[imgCurrent.value]
  getPreviewImgAndPrice()
}

function getPreviewImgAndPrice() {
  previewImgUrl.value = activeSkuItem.value.goodsImage ?? mallData.value.imageUrl
  findMaxPriceCoupon()
}

function getAllGoodsImg() {
  mallData.value.skuList?.forEach((item) => {
    if (item.goodsImage) {
      allGoodsImg.value.push(item.goodsImage)
    }
    else {
      allGoodsImg.value.push(mallData.value.imageUrl)
    }
  })
}

/**
 * 找出面值最大的，最低使用额度满足的代金券
 */
function findMaxPriceCoupon() {
  // 过滤出符合条件的对象，无需判断优惠券类型serverType，因为请求的是serverType===4的数据
  const filteredList = couponList.value.filter(
    item =>
      item.couponType === CouponType.cash
      && item.attrDetailValueId === activeSkuItem.value.attrDetailValueId
      && item.minUsagePrice <= activeSkuItem.value.originalPrice,
  )

  // 如果过滤后的列表为空，则返回null
  if (filteredList.length === 0) {
    bestPriceItem.value = null
    couponPrice.value = 0
    return
  }

  // 在过滤后的列表中找出couponPrice最大的对象
  bestPriceItem.value = filteredList.reduce(
    (max, current) => (current.couponPrice > max.couponPrice ? current : max),
    filteredList[0],
  )

  if (bestPriceItem.value) {
    // 确保判断对象正确
    couponPrice.value = bestPriceItem.value?.couponPrice || 0
  }
}

function showSelectSheet() {
  if (!activeSkuItem.value) {
    activeSkuItem.value = mallData.value.skuList[0]
    couponPageApi({
      isUsed: 0,
      isTimeOut: 0,
      serverType: ServerType.shopMall,
      groupBy: '',
      needTotalCount: true,
      orderBy: '',
      orderDirection: OrderDirection.def,
      pageIndex: 1,
      pageSize: 10000,
      userId: userId.value,
    }).then((res) => {
      couponList.value = res.data
      getPreviewImgAndPrice()
    })
  }
  isSheetShow.value = true
}

function closeSheetShow() {
  isSheetShow.value = false
}

function toPreviewListImg() {
  imgCurrent.value = mallData.value.skuList?.findIndex(
    item => item.attrDetailValueId === activeSkuItem.value.attrDetailValueId,
  )
  isShowPreviewImg.value = true
}

function closePreviewShow() {
  isShowPreviewImg.value = false
}

const activeSkuName = computed(() => {
  return selectArr.value.join('，')
})

function onChangePreviewImg(current: number) {
  // 兼容两种类型的事件参数
  const index = typeof current === 'object' ? (current as any).current : current
  activeSkuItem.value = mallData.value.skuList[index]
  selectArr.value = mallData.value.skuList[index].specValueNameStr.split(',')
}

function handleAddressClick() {
  uni.navigateTo({
    url: '/pages/addressPage/index?toSelect=true',
  })
}

function toDiscountCouponPage() {
  uni.navigateTo({
    url: `/pages/discountCoupon/index?toSelect=true&attrDetailValueId=${activeSkuItem.value.attrDetailValueId}&originalPrice=${activeSkuItem.value.originalPrice}`,
  })
}

onLoad((option) => {
  useCouponStore.resetCouponStore()
  goodsId = Number(option.goodsId)
  // 预防首页太快没登录，这里再自动判断，没登录再登录
  userStore.login().then(() => {
    mallGoodsLoadApi(goodsId).then((res) => {
      if (res.data.goodsStatus === GoodsStatus.listed) {
        mallData.value = res.data
        carousesList.value = [res.data.imageUrl, ...res.data.sliderImages.split('|')]
        // activeSkuItem.value = res.data.skuList[0]
        selectArr.value = res.data.skuList[0].specValueNameStr.split(',')
        getAllGoodsImg()
        // 加载评价数据
        getCommentList()
      }
      else {
        uni.showModal({
          title: '商品已下架',
          showCancel: false,
          success(res) {
            if (res.confirm) {
              uni.navigateBack()
            }
            else if (res.cancel) {
              uni.navigateBack()
            }
          },
        })
      }
    })
  })
})

onShow(() => {
  getAddressData()
  if (isSelectedCoupon.value) {
    findMaxPriceCoupon()
    useCouponStore.resetCouponStore()
  }
})
</script>

<template>
  <mall-swiper :carouses-list="carousesList" :window-width="windowWidth" />
  <view class="rounded-b bg-white">
    <view class="px-4 pb-4 pt-3 text-2xl font-bold">
      {{ mallData?.goodsName }}
    </view>
  </view>
  <view
    class="relative mt-2 overflow-hidden rounded bg-white p-5 text-sm" style="height: 18vw"
    @click="showSelectSheet"
  >
    <view>
      <view class="mt-2 text-base font-bold">
        {{ mallData?.specList[0]?.specName }}
      </view>
      <view class="flex flex-wrap gap-2">
        <view
          v-for="(subItem, subIndex) in mallData?.specList[0]?.specValue" :key="subIndex"
          class="f-tag o-border-gray-tag mt-3"
        >
          {{ subItem }}
        </view>
      </view>
    </view>
    <view class="f-list-hidden absolute bottom-0 left-0 z-1 w-full" />
    <view class="absolute right-6 top-6 flex gap-1 text-gray">
      <view>选择</view>
      <up-icon name="arrow-right" :size="14" />
    </view>
  </view>

  <!-- 商品评价部分 -->
  <view class="mt-2 rounded bg-white p-4">
    <view class="mb-4 flex items-center justify-between">
      <view class="text-lg font-bold">
        商品评价 ({{ commentTotal }})
      </view>
      <view
        v-if="commentTotal > 2" class="flex cursor-pointer items-center gap-1 text-sm text-gray-500"
        @click="showAllComments"
      >
        <view>查看全部</view>
        <up-icon name="arrow-right" :size="14" />
      </view>
    </view>

    <view v-if="commentList.length === 0" class="py-4 text-center text-gray">
      暂无评价
    </view>

    <view v-else>
      <CommentItem
        v-for="(item, index) in commentList" :key="index" :comment="item"
        :show-border="index !== commentList.length - 1" @preview-image="previewCommentImage"
      />
    </view>
  </view>

  <view class="mt-2 overflow-hidden rounded bg-white py-4">
    <up-parse :content="mallData?.goodsContent" />
  </view>
  <view class="p-10" />
  <view class="f-bottom-box fixed bottom-0 left-0 z-2 box-border h-auto w-full flex gap-2 bg-white px-4 pb-3 pt-2">
    <cs-btn-left-button />
    <view
      class="o-bg-primary flex flex-grow-1 items-center justify-center rounded p-3 color-white font-bold"
      @click="showSelectSheet"
    >
      立即购买
    </view>
  </view>
  <up-action-sheet :show="isSheetShow" :close-on-click-overlay="true" :round="10" @close="isSheetShow = false">
    <view
      class="relative flex flex-col px-4 pb-3 pt-4 text-left"
      :style="{ height: `${windowHeight - safeAreaInsets?.top - 160}px` }"
    >
      <up-icon class="absolute right-6 top-6 text-gray" name="close" :size="14" @click="closeSheetShow" />
      <view class="mt-5">
        <view class="flex items-end justify-between gap-4" @click="handleAddressClick">
          <template v-if="addressData.phone">
            <view>
              <view class="flex gap-4">
                <text>{{ addressData.realName }}</text>
                <text>{{ addressData.phone }}</text>
              </view>
              <view class="">
                {{ addressData.district }}{{ addressData.addressDetail }}
              </view>
            </view>
            <view class="o-color-aid shrink-0 pr-1">
              <up-icon name="arrow-right" :size="14" />
            </view>
          </template>
          <view v-else class="flex color-red space-x-1">
            <up-icon name="edit-pen" :color="Color.primary" :size="28" />
            <text class="blink-text">
              请添加收货地址
            </text>
          </view>
        </view>
      </view>
      <view class="o-line mb-2 mt-2" />
      <view class="mb-4 flex gap-3 px-4">
        <up-image
          :width="100" :height="100" :src="allGoodsImg[imgCurrent]" mode="aspectFill"
          @click="toPreviewListImg"
        />
        <view class="flex flex-col justify-between">
          <view>
            <price-box
              :price="parseFloat((activeSkuItem?.originalPrice * quantity).toFixed(2))" not-show-decimal
              :size="40" class="color-red"
            />
            <view v-if="activeSkuItem?.stock === 0" class="mt-2 text-sm text-gray">
              商品暂无库存
            </view>
          </view>
          <up-number-box v-model="quantity" class="mt-6" />
        </view>
      </view>
      <view class="relative mt-4 h-0 flex-1 overflow-auto pb-2">
        <view v-for="(item, index) in mallData?.specList" :key="item?.specId" class="text-sm">
          <view class="mb-2 text-base font-bold">
            {{ item?.specName }}
          </view>
          <view class="mb-3 flex flex-wrap gap-2">
            <view
              v-for="(subItem, subIndex) in item?.specValue" :key="subIndex" class="f-tag flex gap-1"
              :class="isSelectTag(index, subItem) ? 'o-border-blue-tag' : 'o-border-gray-tag'"
              @click="handleSelectTag(index, subItem, mallData?.specList?.length)"
            >
              <text>{{ subItem }}</text>
            </view>
          </view>
        </view>
      </view>
      <view class="o-line my-3" />
      <view class="flex items-center justify-between px-2" @click="toDiscountCouponPage">
        <view>优惠券</view>
        <view class="flex items-center gap-1">
          <template v-if="couponPrice !== 0">
            <view>￥</view>
            <view>{{ couponPrice }}</view>
          </template>
          <view class="o-color-aid">
            <up-icon name="arrow-right" size="14" />
          </view>
        </view>
      </view>
      <view class="o-line mb-8 mt-3" />
      <view
        class="flex items-center justify-center gap-2 rounded p-3 color-white font-bold"
        :class="loading || activeSkuItem?.stock === 0 ? 'o-bg-primary-disable' : 'o-bg-primary'" @click="handleSubmit"
      >
        <view v-if="activeSkuItem?.stock === 0">
          暂无库存
        </view>
        <template v-else>
          <view>立即支付</view>
          <price-box :price="needPayPrice" :size="40" class="color-white" />
        </template>
      </view>
    </view>
  </up-action-sheet>
  <view
    v-if="isShowPreviewImg"
    class="f-img-preview absolute left-0 top-0 flex flex-col items-center justify-center bg-black color-white"
    style="z-index: 99999" @click="closePreviewShow"
  >
    <up-icon
      class="absolute right-6 color-white" name="close" :size="16"
      :style="{ top: `${safeAreaInsets?.top + 60}px` }"
    />
    <view class="w-full">
      <up-swiper
        v-model:current="imgCurrent" :height="windowWidth" :list="allGoodsImg" :autoplay="false"
        indicator-style="top:10px;right: 20px" @change="onChangePreviewImg"
      />
      <view class="mt-4 p-4 text-center">
        {{ activeSkuName }}
      </view>
    </view>
  </view>

  <!-- 全部评论弹窗 -->
  <up-action-sheet :show="isCommentSheetShow" :close-on-click-overlay="true" :round="10" @close="closeCommentSheet">
    <view class="relative px-4 pb-3 pt-4" :style="{ height: `${windowHeight - safeAreaInsets?.top - 160}px` }">
      <view class="mb-4 flex items-center justify-between">
        <view class="text-lg font-bold">
          全部评价 ({{ commentTotal }})
        </view>
        <up-icon class="text-gray" name="close" :size="20" @click="closeCommentSheet" />
      </view>

      <scroll-view
        class="comment-scroll-view" scroll-y
        :style="{ height: `${windowHeight - safeAreaInsets?.top - 180}px` }" @scrolltolower="onCommentScrolltolower"
      >
        <view v-if="allCommentList.length === 0 && !allCommentLoading" class="py-8 text-center text-gray">
          暂无评价
        </view>

        <view v-else class="text-left">
          <template v-for="(item, index) in allCommentList" :key="index">
            <CommentItem
              :comment="item" :show-border="index !== allCommentList.length - 1"
              @preview-image="previewCommentImage"
            />
            <view class="o-line" />
          </template>

          <!-- 加载更多提示 -->
          <view v-if="hasMoreAllComment || allCommentLoading" class="py-4 text-center text-sm text-gray-500">
            <view v-if="allCommentLoading">
              加载中...
            </view>
            <view v-else>
              上拉加载更多
            </view>
          </view>

          <view v-else-if="allCommentList.length > 0" class="py-4 text-center text-sm text-gray-500">
            没有更多评价了
          </view>
        </view>
      </scroll-view>
    </view>
  </up-action-sheet>
</template>

<style lang="scss" scoped>
.f-bottom-box {
  box-shadow: rgba(0, 0, 0, 0.1) -10vw 0px 10vw;
}

.f-list-hidden {
  height: 13vw;
  background: linear-gradient(rgba(255, 255, 255, 0) 0%, #fff 70%);
}

.f-tag {
  @apply rounded px-4 py-1.5;
}

.f-img-preview {
  @apply w-screen h-screen;
}

.f-border-bottom {
  @apply border-b border-gray-200;
}

@keyframes blink {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.3;
  }

  100% {
    opacity: 1;
  }
}

.blink-text {
  animation: blink 1.2s infinite;
  font-weight: bold;
}
</style>
