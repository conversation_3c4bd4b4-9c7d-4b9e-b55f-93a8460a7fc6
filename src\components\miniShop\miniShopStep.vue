<script setup lang="ts">
import { handleCopy } from '@/utils'
</script>

<template>
  <view class="mb-6 pt-2">
    <view class="mb-2 text-sm font-bold">
      开通步骤：
    </view>
    <view class="flex text-xs">
      <view class="f-index o-color-primary flex-shrink-0 font-bold">
        1.
      </view>
      <view>按需求选择合适二维码微站服务方案，并开通。</view>
    </view>
    <view class="flex text-xs">
      <view class="f-index o-color-primary flex-shrink-0 font-bold">
        2.
      </view>
      <view>完成付款后，用电脑打开 www.gs1helper.com 网站，并点击右上角登录按钮。</view>
    </view>
    <view
      class="flex items-center justify-center gap-1 py-2"
      @click="handleCopy('www.gs1helper.com')"
    >
      <view class="o-barcode-gray-card ml-6 rounded px-6 py-1">
        www.gs1helper.com
      </view>
      <up-tag :size="mini" plain type="warning" text="点击复制" />
    </view>
    <view class="flex text-xs">
      <view class="f-index o-color-primary flex-shrink-0 font-bold">
        3.
      </view>
      <view>完成登录后，点击页面上方 二维码微站。</view>
    </view>
    <view class="flex text-xs">
      <view class="f-index o-color-primary flex-shrink-0 font-bold">
        4.
      </view>
      <view>新建商品并上架后，即可下载二维码，扫码即可查看商品等详情。</view>
    </view>
  </view>
</template>

<style scoped lang="scss"></style>
