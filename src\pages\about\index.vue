<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "关于我们"
  }
}
</route>

<script lang="ts" setup>
import { PublicImgPath } from '@/components/image'
import { Article, getArticleName } from '@/enums'

const accountInfo = uni.getAccountInfoSync()

function toStatement() {
  uni.navigateTo({
    url: '/pages/about/statement',
  })
}
</script>

<template>
  <view class="f-page flex flex-col items-center justify-between overflow-hidden px-4 pt-10">
    <view class="flex flex-col items-center text-center">
      <view class="f-app-logo flex items-center gap-2 rounded px-6 py-2">
        <image
          :src="`${PublicImgPath}logo-white.svg`"
          alt="迅码智联"
          mode="heightFix"
          style="height: 10vw"
        />
        <view class="f-tag-card h-fit rounded px-2 py-0.5 text-white font-bold">
          智联
        </view>
      </view>
      <view class="mt-4 text-sm">
        <text>Version</text>
        <text class="pr-1">
          {{ accountInfo.miniProgram.version }}
        </text>
      </view>
      <view class="o-color-aid mt-12 text-sm">
        <view class="o-color-primary font-bold">
          「迅码智联」
        </view>
        <view>提供专业标签打印方案</view>
        <view>的提供服务商</view>
      </view>
    </view>
    <view class="o-color-aid text-center text-xs">
      <view class="mb-1 color-primary" @click="toStatement">
        {{ getArticleName(Article.platform) }}及相关条款
      </view>
      <view>迅码智联 版权所有</view>
      <view>Copyright © xunma All Rights Reserved.</view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-page {
  height: calc(100vh - 200rpx);
}
.f-app-logo {
  background: linear-gradient(90deg, #fa4c03 0%, #ff5b00 48.56%, #fd7424 100%);
}
.f-tag-card {
  background: linear-gradient(94deg, #181818 49.56%, #555 80.48%, #181818 98.01%);
}
</style>
