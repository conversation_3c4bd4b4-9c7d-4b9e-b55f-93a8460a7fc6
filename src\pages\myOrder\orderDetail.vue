<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "订单详情",
    "backgroundColor": "#f0f3f8"
  }
}
</route>

<script lang="ts" setup>
import type { OrderLoadRes } from '@/service/orderApi'
import csLongButton from '@/components/customer/csLongButton.vue'
import PriceBox from '@/components/Price/PriceBox.vue'
import { InvoiceState, OrderStatus } from '@/enums'
import { confirmReceiptApi, orderLoadApi } from '@/service/orderApi'
import { useRefundStore } from '@/store/refundStore'

const refundStore = useRefundStore()

const orderData = ref<OrderLoadRes['data']>()
const loading = ref(false)
const orderPayCode = ref('')

onLoad((options) => {
  if (options.orderPayCode) {
    orderPayCode.value = options.orderPayCode
    getOrderDetail()
  }
})

function getOrderDetail() {
  loading.value = true
  orderLoadApi({ orderPayCode: orderPayCode.value })
    .then((res: OrderLoadRes) => {
      orderData.value = res.data
    })
    .catch(() => {
      uni.showToast({
        title: '订单不存在',
        icon: 'none',
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    })
    .finally(() => {
      loading.value = false
      uni.hideLoading()
    })
}

function getOrderStatusColor(status: OrderStatus) {
  switch (status) {
    case OrderStatus.waitingSend:
      return 'text-orange'
    case OrderStatus.waitingReceive:
      return 'text-primary'
    case OrderStatus.waitingEvaluate:
      return 'text-primary'
    case OrderStatus.done:
      return 'text-gray'
    case OrderStatus.afterSale:
      return 'text-red'
    case OrderStatus.afterSaleEnd:
      return 'text-gray'
    default:
      return 'text-gray'
  }
}

function getInvoiceStateColor(status: InvoiceState) {
  switch (status) {
    case InvoiceState.failed:
      return 'text-red-500'
    case InvoiceState.applying:
      return 'text-primary'
    case InvoiceState.done:
      return 'text-green-500'
    default:
      return 'text-gray-500'
  }
}

function handleViewLogistics() {
  if (orderData.value) {
    uni.navigateTo({
      url: `/pages/myOrder/logisticsDetail?orderCode=${orderData.value.orderCode}`,
    })
  }
}

function handleApplyInvoice() {
/*   if (orderData.value && orderData.value.invoiceState === InvoiceState.notApply) {
    // 跳转到发票申请页面，传递订单信息
    const orderInfo = {
      orderId: orderData.value.orderId,
      serverType: orderData.value.serverType,
      actuallyPrice: orderData.value.actuallyPrice,
    }
    uni.navigateTo({
      url: `/pages/invoice/invoiceInfo?orderData=${encodeURIComponent(JSON.stringify(orderInfo))}`,
    })
  } */
  uni.navigateTo({
    url: '/pages/invoice/orderInvoicing',
  })
}

function handleCopyOrderCode() {
  if (orderData.value) {
    uni.setClipboardData({
      data: orderData.value.orderPayCode,
      success: () => {
        uni.showToast({
          title: '订单号已复制',
          icon: 'success',
        })
      },
    })
  }
}

function handleConfirmReceive() {
  wx?.openBusinessView({
    businessType: 'weappOrderConfirm',
    extraData: {
      transaction_id: orderData.value.transactionNo,
    },
    success() {
      confirmReceiptApi({
        orderCode: orderData.value.orderCode,
      })
        .then((res) => {
          if (res.success) {
            uni.showLoading({
              title: '加载中',
            })
            setTimeout(() => {
              getOrderDetail()
            }, 2000)
          }
        })
        .catch((err) => {
          uni.showToast({
            icon: 'error',
            title: err,
            duration: 3000,
          })
        })
    },
    fail() {
      uni.showToast({
        icon: 'error',
        title: '确认收货失败',
        duration: 3000,
      })
    },
  })
}

function handleReorder() {
  if (orderData.value) {
    uni.showModal({
      title: '再次购买',
      content: '是否要再次购买相同的服务？',
      success: (res) => {
        if (res.confirm) {
          // TODO 再次购买、购买耗材
        }
      },
    })
  }
}

function getOrderStatusText(status: OrderStatus) {
  switch (status) {
    case OrderStatus.waitingSend:
      return '商家正在准备发货，请耐心等待'
    case OrderStatus.waitingReceive:
      return '商品已发货，请注意查收'
    case OrderStatus.waitingEvaluate:
      return '商品已送达，请确认收货并评价'
    case OrderStatus.done:
      return '订单已完成'
    case OrderStatus.afterSale:
      return '售后申请处理中'
    case OrderStatus.afterSaleEnd:
      return '售后已结束'
    default:
      return ''
  }
}

// TODO 未能跳转
function handleGoodsDetail(goodsId: number) {
  uni.navigateTo({
    url: `/pages/goods/goodsDetail?goodsId=${goodsId}`,
  })
}

function handleApplyAfterSale(orderCode: string) {
  if (orderData.value) {
    // 设置售后申请商品信息
    refundStore.setRefundGoodsInfo({
      orderCode,
      goodsName: orderData.value.goodsName,
      orderContent: orderData.value.orderContent,
      goodsImage: orderData.value.goodsImage,
      payDate: orderData.value.payDate,
    })
    uni.navigateTo({
      url: `/pages/myOrder/refundApply?orderCode=${orderCode}`,
    })
  }
}

// 查看售后详情
function viewRefundDetail(refundOrderId: number) {
  uni.navigateTo({
    url: `/pages/myOrder/refundDetail?refundOrderId=${refundOrderId}`,
  })
}
</script>

<template>
  <view class="min-h-screen">
    <template v-if="loading">
      <view class="flex items-center justify-center py-20">
        <view>加载中...</view>
      </view>
    </template>
    <template v-else-if="orderData">
      <!-- 订单状态卡片 -->
      <view class="mx-3 mt-3 rounded-sm bg-white p-4">
        <view class="mb-3 flex items-center justify-between">
          <view class="text-lg font-bold">
            订单状态
          </view>
          <view class="text-lg font-bold" :class="[getOrderStatusColor(orderData.orderStutas)]">
            {{ orderData.orderStutasStr }}
          </view>
        </view>
        <view v-if="getOrderStatusText(orderData.orderStutas)" class="mb-3 text-sm font-bold">
          {{ getOrderStatusText(orderData.orderStutas) }}
        </view>
        <view class="text-sm text-gray-600">
          <view class="mb-1 flex items-center">
            <text>订单编号：{{ orderData.orderPayCode }}</text>
            <view class="ml-2 text-xs text-primary" @click="handleCopyOrderCode">
              复制
            </view>
          </view>
          <view class="mb-1">
            下单时间：{{ orderData.createdDate }}
          </view>
          <view class="mb-1">
            支付时间：{{ orderData.payDate }}
          </view>
        </view>
      </view>

      <!-- 产品内容卡片 -->
      <view class="mx-3 mt-3 rounded-sm bg-white p-4">
        <view class="mb-3 text-lg font-bold">
          购买产品
        </view>
        <view class="mt-2 flex space-x-2" @click="handleGoodsDetail(orderData.goodsId)">
          <image class="f-img" mode="aspectFill" :src="orderData.goodsImage" />
          <view>
            <view class="truncate">
              {{ orderData.goodsName }}
            </view>
            <view class="text-sm text-gray">
              {{ orderData.orderContent }}
            </view>
          </view>
        </view>
      </view>

      <!-- 价格信息卡片 -->
      <view class="mx-3 mt-3 rounded-sm bg-white p-4">
        <view class="mb-3 text-lg font-bold">
          价格信息
        </view>
        <view class="space-y-2">
          <view class="flex justify-between text-sm">
            <view class="text-gray-600">
              商品金额：
            </view>
            <price-box :price="orderData.price" :size="28" />
          </view>
          <view class="flex justify-between text-sm">
            <view class="text-gray-600">
              总金额：
            </view>
            <price-box :price="orderData.totalPrice" :size="28" />
          </view>
          <view class="o-line my-2" />
          <view class="flex justify-between">
            <view class="font-bold">
              实付金额：
            </view>
            <price-box :price="orderData.actuallyPrice" :size="32" color="text-red-500" />
          </view>
        </view>
        <view class="mt-2 flex justify-end">
          <view
            v-if="[OrderStatus.waitingEvaluate, OrderStatus.done].includes(orderData.orderStutas)"
            class="mr-2 center rounded-sm bg-gray-100 px-5 py-2 text-sm"
            @click="handleApplyAfterSale(orderData.orderCode)"
          >
            申请售后
          </view>
          <view
            v-if="[OrderStatus.afterSale, OrderStatus.afterSaleEnd].includes(orderData.orderStutas)"
            class="center rounded-sm bg-gray-100 px-5 py-2 text-sm"
            @click="viewRefundDetail(orderData.refundOrderId)"
          >
            售后详情
          </view>
        </view>
      </view>

      <!-- 发票信息卡片 -->
      <view class="mx-3 mt-3 rounded-sm bg-white p-4">
        <view class="mb-3 flex items-center justify-between">
          <view class="text-lg font-bold">
            发票信息
          </view>
          <view class="text-sm" :class="[getInvoiceStateColor(orderData.invoiceState)]">
            {{ orderData.invoiceStateStr }}
          </view>
        </view>
        <view class="text-sm text-gray-600">
          <view v-if="orderData.invoiceState === InvoiceState.notApply">
            您还未申请发票，可点击下方按钮申请
          </view>
          <view v-else-if="orderData.invoiceState === InvoiceState.applying">
            发票申请已提交，正在处理中
          </view>
          <view v-else-if="orderData.invoiceState === InvoiceState.done">
            发票已开具完成
          </view>
          <view v-else-if="orderData.invoiceState === InvoiceState.failed">
            发票开具失败，请联系客服
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="mx-3 mb-6 mt-3">
        <!-- 主要操作按钮 -->
        <view class="mb-3 flex space-x-2">
          <view
            v-if="
              orderData.invoiceState === InvoiceState.notApply
                && [OrderStatus.waitingEvaluate, OrderStatus.done].includes(orderData.orderStutas)
            "
            class="bg-green-500 flex-1 rounded-sm py-3 text-center text-sm text-white"
            @click="handleApplyInvoice"
          >
            开发票
          </view>
          <view
            v-if="[OrderStatus.waitingReceive, OrderStatus.done].includes(orderData.orderStutas)"
            class="flex-1 rounded-sm bg-white py-3 text-center text-sm"
            @click="handleViewLogistics"
          >
            查看物流
          </view>
          <view
            v-if="orderData.orderStutas === OrderStatus.waitingReceive"
            class="flex-1 rounded-sm bg-primary py-3 text-center text-sm text-white"
            @click="handleConfirmReceive"
          >
            确认收货
          </view>
        </view>
        <cs-long-button />
      </view>
    </template>
    <template v-else>
      <view class="flex flex-col items-center justify-center py-20">
        <view class="text-lg text-gray-500">
          订单不存在
        </view>
        <view class="mt-2 text-sm text-gray-400">
          请检查订单号是否正确
        </view>
      </view>
    </template>
  </view>
</template>

<style lang="scss" scoped>
.f-img {
  $w: 100rpx;
  width: $w;
  height: $w;
}
</style>
